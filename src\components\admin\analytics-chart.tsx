"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface ChartData {
  month: string;
  revenue: number;
  orders: number;
}

interface AnalyticsChartProps {
  data: ChartData[];
  title: string;
  type: "revenue" | "orders";
}

export function AnalyticsChart({ data, title, type }: AnalyticsChartProps) {
  const maxValue = Math.max(
    ...data.map((item) => (type === "revenue" ? item.revenue : item.orders))
  );

  const formatValue = (value: number) => {
    if (type === "revenue") {
      return new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
        notation: "compact",
      }).format(value);
    } else {
      return value.toString();
    }
  };

  return (
    <Card data-testid={`${type}-chart`}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => {
            const value = type === "revenue" ? item.revenue : item.orders;
            const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;

            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{item.month}</span>
                  <span className="font-medium">{formatValue(value)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      type === "revenue"
                        ? "bg-gradient-to-r from-blue-500 to-blue-600"
                        : "bg-gradient-to-r from-green-500 to-green-600"
                    }`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Không có dữ liệu để hiển thị
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface StatusChartProps {
  data: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  title: string;
}

export function StatusChart({ data, title }: StatusChartProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "delivered":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "processing":
        return "bg-blue-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "Hoàn thành";
      case "delivered":
        return "Đã giao";
      case "pending":
        return "Chờ xử lý";
      case "processing":
        return "Đang xử lý";
      case "cancelled":
        return "Đã hủy";
      default:
        return status;
    }
  };

  return (
    <Card data-testid="status-chart">
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusColor(item.status)}`}
                />
                <span className="text-sm font-medium">
                  {getStatusLabel(item.status)}
                </span>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">{item.count}</div>
                <div className="text-xs text-muted-foreground">
                  {item.percentage.toFixed(1)}%
                </div>
              </div>
            </div>
          ))}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Không có dữ liệu để hiển thị
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface TopItemsChartProps {
  data: Array<{
    id: string;
    name: string;
    value: number;
    label?: string;
  }>;
  title: string;
  valueFormatter?: (value: number) => string;
}

export function TopItemsChart({
  data,
  title,
  valueFormatter,
}: TopItemsChartProps) {
  const maxValue = Math.max(...data.map((item) => item.value));

  const defaultFormatter = (value: number) => {
    return new Intl.NumberFormat("vi-VN").format(value);
  };

  const formatValue = valueFormatter || defaultFormatter;

  return (
    <Card data-testid="top-items-chart">
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => {
            const percentage = maxValue > 0 ? (item.value / maxValue) * 100 : 0;

            return (
              <div key={item.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs font-medium text-muted-foreground">
                      #{index + 1}
                    </span>
                    <span className="text-sm font-medium truncate max-w-[200px]">
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium">
                    {formatValue(item.value)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Không có dữ liệu để hiển thị
          </div>
        )}
      </CardContent>
    </Card>
  );
}
