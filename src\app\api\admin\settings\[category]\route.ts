import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

// GET /api/admin/settings/[category] - <PERSON><PERSON><PERSON> settings theo category
export async function GET(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { category } = params;

    // Define which settings belong to each category
    const categoryKeys: Record<string, string[]> = {
      general: ["siteName", "siteDescription", "siteUrl", "logo", "favicon"],
      contact: ["contactEmail", "contactPhone", "address", "socialMedia"],
      payment: ["paymentMethods"],
      shipping: ["shippingSettings"],
      email: ["emailSettings"],
      notifications: ["notifications"],
      seo: ["seoSettings"],
      security: ["securitySettings"],
    };

    const keys = categoryKeys[category];
    if (!keys) {
      return NextResponse.json(
        { error: "Category không hợp lệ" },
        { status: 400 }
      );
    }

    // Get settings for this category
    const settings = await prisma.setting.findMany({
      where: {
        key: {
          in: keys,
        },
      },
    });

    // Convert to key-value object
    const settingsObject: any = {};
    settings.forEach((setting) => {
      settingsObject[setting.key] = setting.value;
    });

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error("Get category settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy cài đặt" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings/[category] - Cập nhật settings theo category
export async function PUT(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { category } = params;
    const body = await request.json();

    // Define which settings belong to each category
    const categoryKeys: Record<string, string[]> = {
      general: ["siteName", "siteDescription", "siteUrl", "logo", "favicon"],
      contact: ["contactEmail", "contactPhone", "address", "socialMedia"],
      payment: ["paymentMethods"],
      shipping: ["shippingSettings"],
      email: ["emailSettings"],
      notifications: ["notifications"],
      seo: ["seoSettings"],
      security: ["securitySettings"],
    };

    const allowedKeys = categoryKeys[category];
    if (!allowedKeys) {
      return NextResponse.json(
        { error: "Category không hợp lệ" },
        { status: 400 }
      );
    }

    // Filter only allowed keys for this category
    const filteredSettings: any = {};
    Object.entries(body).forEach(([key, value]) => {
      if (allowedKeys.includes(key)) {
        filteredSettings[key] = value;
      }
    });

    // Update each setting
    const updatePromises = Object.entries(filteredSettings).map(
      async ([key, value]) => {
        return prisma.setting.upsert({
          where: { key },
          update: {
            value: value as any,
            updatedAt: new Date(),
          },
          create: {
            key,
            value: value as any,
            type: typeof value === "object" ? "json" : typeof value,
          },
        });
      }
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: `Cài đặt ${category} đã được cập nhật thành công`,
    });
  } catch (error) {
    console.error("Update category settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật cài đặt" },
      { status: 500 }
    );
  }
}
