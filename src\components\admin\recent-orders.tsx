"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>zon<PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";

const recentOrders = [
  {
    id: "ORD-001",
    customer: "Nguyễn Văn A",
    email: "nguy<PERSON><PERSON>@email.com",
    total: 1250000,
    status: "PENDING",
    date: "2024-01-15",
  },
  {
    id: "ORD-002",
    customer: "Tr<PERSON><PERSON> Thị B",
    email: "<EMAIL>",
    total: 890000,
    status: "CONFIRMED",
    date: "2024-01-15",
  },
  {
    id: "ORD-003",
    customer: "Lê Văn C",
    email: "<EMAIL>",
    total: 2100000,
    status: "SHIPPED",
    date: "2024-01-14",
  },
  {
    id: "ORD-004",
    customer: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    total: 650000,
    status: "DELIVERED",
    date: "2024-01-14",
  },
  {
    id: "ORD-005",
    customer: "<PERSON>àng Văn E",
    email: "<EMAIL>",
    total: 1800000,
    status: "CANCELLED",
    date: "2024-01-13",
  },
];

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  CONFIRMED: "bg-blue-100 text-blue-800",
  PROCESSING: "bg-purple-100 text-purple-800",
  SHIPPED: "bg-indigo-100 text-indigo-800",
  DELIVERED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const statusLabels = {
  PENDING: "Chờ xử lý",
  CONFIRMED: "Đã xác nhận",
  PROCESSING: "Đang xử lý",
  SHIPPED: "Đã gửi",
  DELIVERED: "Đã giao",
  CANCELLED: "Đã hủy",
};

export function RecentOrders() {
  return (
    <Card data-testid="recent-orders">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Đơn hàng gần đây</CardTitle>
        <Button variant="outline" size="sm">
          Xem tất cả
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4" data-testid="orders-list">
          {recentOrders.map((order) => (
            <div
              key={order.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="space-y-1">
                  <div className="font-medium">{order.id}</div>
                  <div className="text-sm text-muted-foreground">
                    {order.customer}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {order.email}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="font-medium">
                    {formatCurrency(order.total)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {order.date}
                  </div>
                </div>

                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    statusColors[order.status as keyof typeof statusColors]
                  }`}
                >
                  {statusLabels[order.status as keyof typeof statusLabels]}
                </span>

                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
