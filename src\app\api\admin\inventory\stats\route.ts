import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/inventory/stats - Get inventory statistics
export async function GET(request: NextRequest) {
  try {
    // Get total products with inventory
    const totalProducts = await prisma.inventoryEntry.count();

    // Get total stock value
    const inventoryWithProducts = await prisma.inventoryEntry.findMany({
      include: {
        product: {
          select: {
            price: true,
          },
        },
      },
    });

    const totalStock = inventoryWithProducts.reduce((sum, entry) => sum + entry.quantity, 0);
    const totalValue = inventoryWithProducts.reduce(
      (sum, entry) => sum + (entry.quantity * entry.product.price),
      0
    );

    // Get low stock products (available <= minStock)
    const lowStockProducts = await prisma.inventoryEntry.count({
      where: {
        available: {
          lte: prisma.inventoryEntry.fields.minStock,
        },
        minStock: {
          gt: 0,
        },
      },
    });

    // Get out of stock products
    const outOfStockProducts = await prisma.inventoryEntry.count({
      where: {
        available: {
          lte: 0,
        },
      },
    });

    // Get recent stock movements
    const recentMovements = await prisma.stockMovement.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      include: {
        inventoryEntry: {
          include: {
            product: {
              select: {
                name: true,
                sku: true,
                images: true,
              },
            },
          },
        },
      },
    });

    // Get inventory alerts (low stock and out of stock)
    const lowStockEntries = await prisma.inventoryEntry.findMany({
      where: {
        OR: [
          {
            available: {
              lte: prisma.inventoryEntry.fields.minStock,
            },
            minStock: {
              gt: 0,
            },
          },
          {
            available: {
              lte: 0,
            },
          },
        ],
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            images: true,
          },
        },
      },
      take: 20,
    });

    const alerts = lowStockEntries.map((entry) => ({
      id: entry.id,
      productId: entry.productId,
      product: entry.product,
      type: entry.available <= 0 ? "OUT_OF_STOCK" : "LOW_STOCK",
      message: entry.available <= 0 
        ? `Sản phẩm ${entry.product.name} đã hết hàng`
        : `Sản phẩm ${entry.product.name} sắp hết hàng (còn ${entry.available}/${entry.minStock})`,
      threshold: entry.minStock,
      currentStock: entry.available,
      createdAt: new Date(),
    }));

    const stats = {
      totalProducts,
      totalStock,
      lowStockProducts,
      outOfStockProducts,
      totalValue,
      recentMovements,
      alerts,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Get inventory stats error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thống kê kho hàng" },
      { status: 500 }
    );
  }
}
