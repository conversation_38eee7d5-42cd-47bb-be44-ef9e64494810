"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  Refresh<PERSON><PERSON>,
  Filter,
  CheckCheck,
  Trash2,
  Search,
  ChevronLeft,
  ChevronRight,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useNotifications } from "@/contexts/NotificationContext";
import { NotificationItem } from "./NotificationItem";
import { NotificationFilters } from "@/types/notification";

interface NotificationListProps {
  showFilters?: boolean;
  showBulkActions?: boolean;
  pageSize?: number;
}

export function NotificationList({
  showFilters = true,
  showBulkActions = true,
  pageSize = 10,
}: NotificationListProps) {
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAllAsRead,
    refreshNotifications,
  } = useNotifications();

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<NotificationFilters>({
    search: "",
    type: "",
    priority: "",
    isRead: "",
  });
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter((notification) => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      if (
        !notification.title.toLowerCase().includes(searchLower) &&
        !notification.message.toLowerCase().includes(searchLower)
      ) {
        return false;
      }
    }

    if (filters.type && notification.type !== filters.type) {
      return false;
    }

    if (filters.priority && notification.priority !== filters.priority) {
      return false;
    }

    if (filters.isRead) {
      const isRead = filters.isRead === "true";
      if (notification.isRead !== isRead) {
        return false;
      }
    }

    return true;
  });

  // Pagination
  const totalPages = Math.ceil(filteredNotifications.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentNotifications = filteredNotifications.slice(
    startIndex,
    endIndex
  );

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(currentNotifications.map((n) => n.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds((prev) => [...prev, id]);
    } else {
      setSelectedIds((prev) => prev.filter((selectedId) => selectedId !== id));
    }
  };

  const handleBulkMarkAsRead = async () => {
    try {
      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "mark_read",
          notificationIds: selectedIds,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        refreshNotifications();
        setSelectedIds([]);
        // Show success message if toast is available
        if (typeof window !== "undefined" && (window as any).toast) {
          (window as any).toast.success(data.message);
        }
      } else {
        const error = await response.json();
        console.error("Bulk mark as read failed:", error);
      }
    } catch (error) {
      console.error("Bulk mark as read error:", error);
    }
  };

  const handleBulkMarkAsUnread = async () => {
    try {
      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "mark_unread",
          notificationIds: selectedIds,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        refreshNotifications();
        setSelectedIds([]);
        // Show success message if toast is available
        if (typeof window !== "undefined" && (window as any).toast) {
          (window as any).toast.success(data.message);
        }
      } else {
        const error = await response.json();
        console.error("Bulk mark as unread failed:", error);
      }
    } catch (error) {
      console.error("Bulk mark as unread error:", error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "delete",
          notificationIds: selectedIds,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        refreshNotifications();
        setSelectedIds([]);
        // Show success message if toast is available
        if (typeof window !== "undefined" && (window as any).toast) {
          (window as any).toast.success(data.message);
        }
      } else {
        const error = await response.json();
        console.error("Bulk delete failed:", error);
      }
    } catch (error) {
      console.error("Bulk delete error:", error);
    }
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      type: "",
      priority: "",
      isRead: "",
    });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Thông báo</h2>
          <p className="text-muted-foreground">
            Quản lý tất cả thông báo trong hệ thống
          </p>
        </div>
        <div className="flex items-center gap-2">
          {showFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Bộ lọc
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={refreshNotifications}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Làm mới
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{notifications.length}</div>
            <p className="text-sm text-muted-foreground">Tổng số thông báo</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {unreadCount}
            </div>
            <p className="text-sm text-muted-foreground">Chưa đọc</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {notifications.length - unreadCount}
            </div>
            <p className="text-sm text-muted-foreground">Đã đọc</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {filteredNotifications.length}
            </div>
            <p className="text-sm text-muted-foreground">Sau lọc</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters Panel */}
      {showFiltersPanel && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Bộ lọc</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Tìm kiếm</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm tiêu đề, nội dung..."
                    value={filters.search}
                    onChange={(e) =>
                      setFilters((prev) => ({
                        ...prev,
                        search: e.target.value,
                      }))
                    }
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Loại</label>
                <Select
                  value={filters.type || "all"}
                  onValueChange={(value) =>
                    setFilters((prev) => ({
                      ...prev,
                      type: value === "all" ? "" : value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tất cả loại" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả loại</SelectItem>
                    <SelectItem value="INFO">Thông tin</SelectItem>
                    <SelectItem value="SUCCESS">Thành công</SelectItem>
                    <SelectItem value="WARNING">Cảnh báo</SelectItem>
                    <SelectItem value="ERROR">Lỗi</SelectItem>
                    <SelectItem value="SYSTEM">Hệ thống</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Độ ưu tiên</label>
                <Select
                  value={filters.priority || "all"}
                  onValueChange={(value) =>
                    setFilters((prev) => ({
                      ...prev,
                      priority: value === "all" ? "" : value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tất cả mức độ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả mức độ</SelectItem>
                    <SelectItem value="LOW">Thấp</SelectItem>
                    <SelectItem value="NORMAL">Bình thường</SelectItem>
                    <SelectItem value="HIGH">Cao</SelectItem>
                    <SelectItem value="URGENT">Khẩn cấp</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Read Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Trạng thái</label>
                <Select
                  value={filters.isRead || "all"}
                  onValueChange={(value) =>
                    setFilters((prev) => ({
                      ...prev,
                      isRead: value === "all" ? "" : value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tất cả trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả trạng thái</SelectItem>
                    <SelectItem value="false">Chưa đọc</SelectItem>
                    <SelectItem value="true">Đã đọc</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={clearFilters}>
                Xóa bộ lọc
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Actions */}
      {showBulkActions && selectedIds.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                Đã chọn {selectedIds.length} thông báo
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkMarkAsRead}
                >
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Đánh dấu đã đọc
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkMarkAsUnread}
                >
                  <X className="h-4 w-4 mr-2" />
                  Đánh dấu chưa đọc
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notifications List */}
      <Card>
        <CardContent className="p-0">
          {error ? (
            <div className="p-8 text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={refreshNotifications}>Thử lại</Button>
            </div>
          ) : isLoading ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Đang tải...</p>
            </div>
          ) : currentNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-muted-foreground">Không có thông báo nào</p>
            </div>
          ) : (
            <div className="space-y-1">
              {/* Select All Header */}
              {showBulkActions && (
                <div className="flex items-center gap-3 p-4 border-b">
                  <Checkbox
                    checked={selectedIds.length === currentNotifications.length}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm font-medium">Chọn tất cả</span>
                </div>
              )}

              {/* Notification Items */}
              {currentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className="flex items-start gap-3 p-4 border-b last:border-b-0"
                >
                  {showBulkActions && (
                    <Checkbox
                      checked={selectedIds.includes(notification.id)}
                      onCheckedChange={(checked) =>
                        handleSelectItem(notification.id, checked as boolean)
                      }
                      className="mt-4"
                    />
                  )}
                  <div className="flex-1">
                    <NotificationItem
                      notification={notification}
                      compact={false}
                      showActions={true}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Hiển thị {startIndex + 1} -{" "}
            {Math.min(endIndex, filteredNotifications.length)} trong tổng số{" "}
            {filteredNotifications.length} thông báo
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Trước
            </Button>
            <span className="text-sm">
              Trang {currentPage} / {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) => Math.min(totalPages, prev + 1))
              }
              disabled={currentPage === totalPages}
            >
              Sau
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
