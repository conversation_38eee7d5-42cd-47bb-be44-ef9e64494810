import { prisma } from "@/lib/prisma";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

interface AuditLogData {
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  adminId: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Log admin actions to audit trail
 */
export async function logAdminAction(data: AuditLogData) {
  try {
    await prisma.auditLog.create({
      data: {
        action: data.action,
        resource: data.resource,
        resourceId: data.resourceId,
        oldValues: data.oldValues,
        newValues: data.newValues,
        description: data.description,
        adminId: data.adminId,
        ipAddress: data.ipAddress || "unknown",
        userAgent: data.userAgent || "unknown",
      },
    });
  } catch (error) {
    console.error("Failed to log admin action:", error);
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Extract IP address and User Agent from request
 */
export function getRequestMetadata(request: NextRequest) {
  const ipAddress =
    request.headers.get("x-forwarded-for") ||
    request.headers.get("x-real-ip") ||
    "unknown";

  const userAgent = request.headers.get("user-agent") || "unknown";

  return { ipAddress, userAgent };
}

/**
 * Middleware wrapper for automatic audit logging
 */
export function withAuditLog(
  action: string,
  resource: string,
  options: {
    getResourceId?: (request: NextRequest, body?: any) => string | undefined;
    getOldValues?: (
      request: NextRequest,
      body?: any
    ) => Promise<Record<string, any> | undefined>;
    getNewValues?: (
      request: NextRequest,
      body?: any
    ) => Record<string, any> | undefined;
    getDescription?: (request: NextRequest, body?: any) => string | undefined;
  } = {}
) {
  return function (
    originalFunction: (request: NextRequest, ...args: any[]) => Promise<any>
  ) {
    return async function (request: NextRequest, ...args: any[]): Promise<any> {
      try {
        // Get admin session
        const session = await getServerSession(adminAuthOptions);
        if (!session?.user || session.user.type !== "admin") {
          return originalFunction(request, ...args);
        }

        const { ipAddress, userAgent } = getRequestMetadata(request);
        let body: any = null;

        // Try to parse request body for POST/PUT requests
        if (request.method === "POST" || request.method === "PUT") {
          try {
            const clonedRequest = request.clone();
            body = await clonedRequest.json();
          } catch {
            // Body might not be JSON or already consumed
          }
        }

        // Get old values before operation (for updates)
        let oldValues: Record<string, any> | undefined;
        if (options.getOldValues) {
          oldValues = await options.getOldValues(request, body);
        }

        // Execute the original function
        const result = await originalFunction(request, ...args);

        // Log the action after successful execution
        await logAdminAction({
          action,
          resource,
          resourceId: options.getResourceId?.(request, body),
          oldValues,
          newValues: options.getNewValues?.(request, body),
          description: options.getDescription?.(request, body),
          adminId: session.user.id,
          ipAddress,
          userAgent,
        });

        return result;
      } catch (error) {
        // Log failed operations too
        const session = await getServerSession(adminAuthOptions);
        if (session?.user && session.user.type === "admin") {
          const { ipAddress, userAgent } = getRequestMetadata(request);

          await logAdminAction({
            action: `${action}_FAILED`,
            resource,
            description: `Failed to ${action.toLowerCase()} ${resource}: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
            adminId: session.user.id,
            ipAddress,
            userAgent,
          });
        }

        throw error;
      }
    };
  };
}

/**
 * Helper function to extract changed fields between old and new objects
 */
export function getChangedFields(
  oldObj: Record<string, any>,
  newObj: Record<string, any>
) {
  const changes: Record<string, { old: any; new: any }> = {};

  // Check for changed and new fields
  for (const key in newObj) {
    if (oldObj[key] !== newObj[key]) {
      changes[key] = {
        old: oldObj[key],
        new: newObj[key],
      };
    }
  }

  // Check for removed fields
  for (const key in oldObj) {
    if (!(key in newObj)) {
      changes[key] = {
        old: oldObj[key],
        new: undefined,
      };
    }
  }

  return changes;
}

/**
 * Common audit log actions
 */
export const AUDIT_ACTIONS = {
  CREATE: "CREATE",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
  LOGIN: "LOGIN",
  LOGOUT: "LOGOUT",
  VIEW: "VIEW",
  EXPORT: "EXPORT",
  IMPORT: "IMPORT",
  ACTIVATE: "ACTIVATE",
  DEACTIVATE: "DEACTIVATE",
  APPROVE: "APPROVE",
  REJECT: "REJECT",
  PUBLISH: "PUBLISH",
  UNPUBLISH: "UNPUBLISH",
} as const;

/**
 * Common resource types
 */
export const AUDIT_RESOURCES = {
  ADMIN_USER: "AdminUser",
  USER: "User",
  PRODUCT: "Product",
  CATEGORY: "Category",
  ORDER: "Order",
  BRAND: "Brand",
  POST: "Post",
  PAGE: "Page",
  NOTIFICATION: "Notification",
  SETTING: "Setting",
  INVENTORY: "Inventory",
} as const;

/**
 * Helper function to create audit log for CRUD operations
 */
export async function logCrudOperation(
  action: keyof typeof AUDIT_ACTIONS,
  resource: keyof typeof AUDIT_RESOURCES,
  resourceId: string,
  adminId: string,
  options: {
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    description?: string;
    ipAddress?: string;
    userAgent?: string;
  } = {}
) {
  const description =
    options.description || `${action} ${resource} ${resourceId}`;

  await logAdminAction({
    action: AUDIT_ACTIONS[action],
    resource: AUDIT_RESOURCES[resource],
    resourceId,
    oldValues: options.oldValues,
    newValues: options.newValues,
    description,
    adminId,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent,
  });
}

/**
 * Helper function to create audit log for authentication events
 */
export async function logAuthEvent(
  action: "LOGIN" | "LOGOUT",
  adminId: string,
  options: {
    description?: string;
    ipAddress?: string;
    userAgent?: string;
  } = {}
) {
  const description = options.description || `Admin ${action.toLowerCase()}`;

  await logAdminAction({
    action,
    resource: "AdminUser",
    resourceId: adminId,
    description,
    adminId,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent,
  });
}

/**
 * Helper function to create audit log for system events
 */
export async function logSystemEvent(
  action: string,
  adminId: string,
  options: {
    resource?: string;
    resourceId?: string;
    description?: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  } = {}
) {
  await logAdminAction({
    action,
    resource: options.resource || "System",
    resourceId: options.resourceId,
    description: options.description,
    newValues: options.metadata,
    adminId,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent,
  });
}
