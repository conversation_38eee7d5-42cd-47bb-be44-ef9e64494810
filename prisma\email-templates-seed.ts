import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Email template data structure (for future EmailTemplate model)
export interface EmailTemplateData {
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  category: string;
  isActive: boolean;
  description: string;
}

// Sample email templates data
export const emailTemplatesData: EmailTemplateData[] = [
  {
    name: "welcome_email",
    subject: "Chào mừng {{customerName}} đến với NS Shop! 🎉",
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Chào mừng đến với NS Shop</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #be185d); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Chào mừng {{customerName}}!</h1>
            <p>Cảm ơn bạn đã đăng ký tài khoản tại NS Shop</p>
        </div>
        <div class="content">
            <h2>Tài khoản của bạn đã sẵn sàng</h2>
            <p>Chúng tôi rất vui mừng được chào đón bạn vào cộng đồng thời trang NS Shop!</p>
            <p><strong>Email:</strong> {{customerEmail}}</p>
            <p><strong>Tên:</strong> {{customerName}}</p>
            <a href="{{loginUrl}}" class="button">Đăng nhập ngay</a>
            <p>Khám phá bộ sưu tập thời trang mới nhất và nhận ưu đãi đặc biệt dành riêng cho thành viên mới.</p>
        </div>
        <div class="footer">
            <p>Cần hỗ trợ? Liên hệ: {{supportEmail}} | {{supportPhone}}</p>
            <p>© 2024 NS Shop. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Chào mừng {{customerName}} đến với NS Shop!

Cảm ơn bạn đã đăng ký tài khoản. Tài khoản của bạn đã sẵn sàng sử dụng.

Thông tin tài khoản:
- Email: {{customerEmail}}
- Tên: {{customerName}}

Đăng nhập tại: {{loginUrl}}

Cần hỗ trợ? Liên hệ: {{supportEmail}}

© 2024 NS Shop. All rights reserved.`,
    variables: ["customerName", "customerEmail", "loginUrl", "supportEmail", "supportPhone"],
    category: "authentication",
    isActive: true,
    description: "Email chào mừng gửi cho khách hàng mới đăng ký tài khoản"
  },
  {
    name: "order_confirmation",
    subject: "Xác nhận đơn hàng #{{orderNumber}} - NS Shop",
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Xác nhận đơn hàng</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #10b981; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
        .order-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .item { border-bottom: 1px solid #e5e7eb; padding: 15px 0; }
        .total { font-weight: bold; font-size: 18px; color: #ec4899; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Đơn hàng đã được xác nhận!</h1>
            <p>Đơn hàng #{{orderNumber}}</p>
        </div>
        <div class="content">
            <div class="order-info">
                <h3>Thông tin đơn hàng</h3>
                <p><strong>Mã đơn hàng:</strong> {{orderNumber}}</p>
                <p><strong>Ngày đặt:</strong> {{orderDate}}</p>
                <p><strong>Trạng thái:</strong> {{orderStatus}}</p>
                
                <h4>Sản phẩm đã đặt:</h4>
                {{#each items}}
                <div class="item">
                    <p><strong>{{name}}</strong></p>
                    <p>Số lượng: {{quantity}} | Giá: {{price}}</p>
                </div>
                {{/each}}
                
                <p class="total">Tổng cộng: {{totalAmount}}</p>
            </div>
            
            <div class="order-info">
                <h3>Địa chỉ giao hàng</h3>
                <p>{{shippingAddress.fullName}}</p>
                <p>{{shippingAddress.address}}</p>
                <p>{{shippingAddress.city}}</p>
                <p>SĐT: {{shippingAddress.phone}}</p>
            </div>
        </div>
    </div>
</body>
</html>`,
    textContent: `Xác nhận đơn hàng #{{orderNumber}}

Cảm ơn bạn đã đặt hàng tại NS Shop!

Thông tin đơn hàng:
- Mã đơn hàng: {{orderNumber}}
- Ngày đặt: {{orderDate}}
- Trạng thái: {{orderStatus}}
- Tổng cộng: {{totalAmount}}

Địa chỉ giao hàng:
{{shippingAddress.fullName}}
{{shippingAddress.address}}
{{shippingAddress.city}}
SĐT: {{shippingAddress.phone}}

Chúng tôi sẽ liên hệ với bạn sớm nhất để xác nhận và giao hàng.

© 2024 NS Shop. All rights reserved.`,
    variables: ["orderNumber", "orderDate", "orderStatus", "totalAmount", "items", "shippingAddress"],
    category: "order",
    isActive: true,
    description: "Email xác nhận đơn hàng gửi cho khách hàng sau khi đặt hàng thành công"
  },
  {
    name: "password_reset",
    subject: "Đặt lại mật khẩu - NS Shop",
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Đặt lại mật khẩu</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f59e0b; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Đặt lại mật khẩu</h1>
        </div>
        <div class="content">
            <p>Xin chào {{customerName}},</p>
            <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.</p>
            <a href="{{resetUrl}}" class="button">Đặt lại mật khẩu</a>
            <div class="warning">
                <p><strong>Lưu ý:</strong> Link này sẽ hết hạn sau {{expiryTime}} phút.</p>
            </div>
            <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Đặt lại mật khẩu - NS Shop

Xin chào {{customerName}},

Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.

Đặt lại mật khẩu tại: {{resetUrl}}

Lưu ý: Link này sẽ hết hạn sau {{expiryTime}} phút.

Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.

© 2024 NS Shop. All rights reserved.`,
    variables: ["customerName", "resetUrl", "expiryTime"],
    category: "authentication",
    isActive: true,
    description: "Email đặt lại mật khẩu gửi cho khách hàng khi quên mật khẩu"
  },
  {
    name: "order_shipped",
    subject: "Đơn hàng #{{orderNumber}} đã được giao cho đơn vị vận chuyển",
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Đơn hàng đã được giao vận chuyển</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3b82f6; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
        .tracking-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .tracking-number { font-size: 24px; font-weight: bold; color: #3b82f6; }
        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Đơn hàng đang trên đường đến bạn!</h1>
            <p>Đơn hàng #{{orderNumber}}</p>
        </div>
        <div class="content">
            <p>Xin chào {{customerName}},</p>
            <p>Đơn hàng của bạn đã được giao cho đơn vị vận chuyển và đang trên đường đến địa chỉ của bạn.</p>
            
            <div class="tracking-info">
                <h3>Mã vận đơn</h3>
                <div class="tracking-number">{{trackingNumber}}</div>
                <p>Đơn vị vận chuyển: {{shippingCarrier}}</p>
                <a href="{{trackingUrl}}" class="button">Theo dõi đơn hàng</a>
            </div>
            
            <p><strong>Thời gian giao hàng dự kiến:</strong> {{estimatedDelivery}}</p>
            <p>Bạn sẽ nhận được thông báo khi đơn hàng được giao thành công.</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Đơn hàng #{{orderNumber}} đã được giao vận chuyển

Xin chào {{customerName}},

Đơn hàng của bạn đã được giao cho đơn vị vận chuyển.

Mã vận đơn: {{trackingNumber}}
Đơn vị vận chuyển: {{shippingCarrier}}
Thời gian giao hàng dự kiến: {{estimatedDelivery}}

Theo dõi đơn hàng tại: {{trackingUrl}}

© 2024 NS Shop. All rights reserved.`,
    variables: ["orderNumber", "customerName", "trackingNumber", "shippingCarrier", "trackingUrl", "estimatedDelivery"],
    category: "order",
    isActive: true,
    description: "Email thông báo đơn hàng đã được giao cho đơn vị vận chuyển"
  },
  {
    name: "promotional_newsletter",
    subject: "🔥 Ưu đãi đặc biệt - Giảm giá lên đến {{discountPercent}}%!",
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ưu đãi đặc biệt</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #be185d); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
        .promo-code { background: #fef3c7; border: 2px dashed #f59e0b; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
        .code { font-size: 24px; font-weight: bold; color: #f59e0b; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .product-grid { display: flex; flex-wrap: wrap; gap: 15px; margin: 20px 0; }
        .product { flex: 1; min-width: 150px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Ưu đãi đặc biệt!</h1>
            <p>Giảm giá lên đến {{discountPercent}}%</p>
        </div>
        <div class="content">
            <p>Xin chào {{customerName}},</p>
            <p>Chúng tôi có tin tuyệt vời dành cho bạn! Hãy tận dụng ưu đãi đặc biệt này để sở hữu những sản phẩm thời trang yêu thích.</p>
            
            <div class="promo-code">
                <p>Mã giảm giá của bạn:</p>
                <div class="code">{{promoCode}}</div>
                <p>Có hiệu lực đến: {{expiryDate}}</p>
            </div>
            
            <a href="{{shopUrl}}" class="button">Mua sắm ngay</a>
            
            <h3>Sản phẩm nổi bật</h3>
            <div class="product-grid">
                {{#each featuredProducts}}
                <div class="product">
                    <img src="{{image}}" alt="{{name}}" style="width: 100%; border-radius: 8px;">
                    <p><strong>{{name}}</strong></p>
                    <p style="color: #ec4899;">{{price}}</p>
                </div>
                {{/each}}
            </div>
        </div>
    </div>
</body>
</html>`,
    textContent: `🔥 Ưu đãi đặc biệt - Giảm giá lên đến {{discountPercent}}%!

Xin chào {{customerName}},

Chúng tôi có tin tuyệt vời dành cho bạn! Hãy tận dụng ưu đãi đặc biệt này.

Mã giảm giá: {{promoCode}}
Có hiệu lực đến: {{expiryDate}}

Mua sắm ngay tại: {{shopUrl}}

© 2024 NS Shop. All rights reserved.`,
    variables: ["customerName", "discountPercent", "promoCode", "expiryDate", "shopUrl", "featuredProducts"],
    category: "marketing",
    isActive: true,
    description: "Email khuyến mãi gửi cho khách hàng về các ưu đãi đặc biệt"
  }
];

// Function to seed email templates (for future use when EmailTemplate model is added)
export async function seedEmailTemplates() {
  console.log("🌱 Seeding email templates...");
  
  // This function will be used when EmailTemplate model is added to schema
  // For now, we just log the templates that would be created
  
  console.log(`📧 Email templates ready for seeding:`);
  emailTemplatesData.forEach((template, index) => {
    console.log(`   ${index + 1}. ${template.name} (${template.category})`);
  });
  
  console.log(`📧 Total email templates: ${emailTemplatesData.length}`);
  
  // TODO: Uncomment when EmailTemplate model is added to schema
  /*
  for (const templateData of emailTemplatesData) {
    await prisma.emailTemplate.upsert({
      where: { name: templateData.name },
      update: {},
      create: templateData,
    });
  }
  */
}
