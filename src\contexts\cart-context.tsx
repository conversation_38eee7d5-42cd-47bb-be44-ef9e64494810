"use client";

import {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useReducer,
  useEffect,
  ReactNode,
} from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  product: {
    id: string;
    name: string;
    price: number;
    salePrice?: number;
    images: string[];
    slug: string;
    stock: number;
    status: string;
  };
}

interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  subtotal: number;
  itemCount: number;
}

interface CartState {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
}

type CartAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_CART"; payload: Cart }
  | { type: "SET_ERROR"; payload: string }
  | { type: "CLEAR_CART" }
  | { type: "UPDATE_ITEM"; payload: { itemId: string; quantity: number } }
  | { type: "REMOVE_ITEM"; payload: string };

const initialState: CartState = {
  cart: null,
  loading: false,
  error: null,
};

function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, loading: action.payload };
    case "SET_CART":
      return { ...state, cart: action.payload, loading: false, error: null };
    case "SET_ERROR":
      return { ...state, error: action.payload, loading: false };
    case "CLEAR_CART":
      return { ...state, cart: null };
    case "UPDATE_ITEM": {
      if (!state.cart) return state;
      const updatedItems = state.cart.items.map((item) =>
        item.id === action.payload.itemId
          ? { ...item, quantity: action.payload.quantity }
          : item
      );
      const subtotal = updatedItems.reduce((sum, item) => {
        const price = item.product.salePrice || item.product.price;
        return sum + price * item.quantity;
      }, 0);
      const itemCount = updatedItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedItems,
          subtotal,
          itemCount,
        },
      };
    }
    case "REMOVE_ITEM": {
      if (!state.cart) return state;
      const filteredItems = state.cart.items.filter(
        (item) => item.id !== action.payload
      );
      const newSubtotal = filteredItems.reduce((sum, item) => {
        const price = item.product.salePrice || item.product.price;
        return sum + price * item.quantity;
      }, 0);
      const newItemCount = filteredItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        cart: {
          ...state.cart,
          items: filteredItems,
          subtotal: newSubtotal,
          itemCount: newItemCount,
        },
      };
    }
    default:
      return state;
  }
}

interface CartContextType {
  state: CartState;
  fetchCart: () => Promise<void>;
  addToCart: (productId: string, quantity: number) => Promise<void>;
  updateCartItem: (itemId: string, quantity: number) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  clearCart: () => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);
  const { data: session } = useSession();

  // Fetch cart from API
  const fetchCart = async () => {
    if (!session) {
      dispatch({ type: "CLEAR_CART" });
      return;
    }

    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const response = await fetch("/api/cart");
      const data = await response.json();

      if (response.ok) {
        dispatch({ type: "SET_CART", payload: data });
      } else {
        dispatch({ type: "SET_ERROR", payload: data.error || "Có lỗi xảy ra" });
      }
    } catch (error) {
      dispatch({
        type: "SET_ERROR",
        payload: "Có lỗi xảy ra khi tải giỏ hàng",
      });
    }
  };

  // Add item to cart
  const addToCart = async (productId: string, quantity: number) => {
    if (!session) {
      toast.error("Vui lòng đăng nhập để thêm vào giỏ hàng");
      return;
    }

    try {
      const response = await fetch("/api/cart", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ productId, quantity }),
      });

      const data = await response.json();

      if (response.ok) {
        dispatch({ type: "SET_CART", payload: data.cart });
        toast.success("Đã thêm vào giỏ hàng");
      } else {
        toast.error(data.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi thêm vào giỏ hàng");
    }
  };

  // Update cart item quantity
  const updateCartItem = async (itemId: string, quantity: number) => {
    if (!session) return;

    // Optimistic update
    dispatch({ type: "UPDATE_ITEM", payload: { itemId, quantity } });

    try {
      const response = await fetch(`/api/cart/${itemId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ quantity }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Revert optimistic update on error
        fetchCart();
        toast.error(data.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      // Revert optimistic update on error
      fetchCart();
      toast.error("Có lỗi xảy ra khi cập nhật giỏ hàng");
    }
  };

  // Remove item from cart
  const removeFromCart = async (itemId: string) => {
    if (!session) return;

    // Optimistic update
    dispatch({ type: "REMOVE_ITEM", payload: itemId });

    try {
      const response = await fetch(`/api/cart/${itemId}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Đã xóa khỏi giỏ hàng");
      } else {
        // Revert optimistic update on error
        fetchCart();
        toast.error(data.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      // Revert optimistic update on error
      fetchCart();
      toast.error("Có lỗi xảy ra khi xóa khỏi giỏ hàng");
    }
  };

  // Clear cart
  const clearCart = () => {
    dispatch({ type: "CLEAR_CART" });
  };

  // Fetch cart when session changes
  useEffect(() => {
    if (session) {
      fetchCart();
    } else {
      clearCart();
    }
  }, [session]);

  const value: CartContextType = {
    state,
    fetchCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
}
