"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, GripVertical, Save, X, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import {
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
  ATTRIBUTE_TYPE_DESCRIPTIONS,
  AttributeFormData,
  AttributeValueFormData,
} from "@/types/attribute";

interface AttributeFormProps {
  initialData?: Partial<AttributeFormData>;
  onSubmit: (data: AttributeFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: "create" | "edit";
}

export function AttributeForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = "create",
}: AttributeFormProps) {
  const [formData, setFormData] = useState<AttributeFormData>({
    name: "",
    slug: "",
    description: "",
    type: "TEXT",
    isRequired: false,
    isFilterable: true,
    sortOrder: 0,
    values: [],
    ...initialData,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name && !initialData?.slug) {
      const slug = formData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");

      setFormData((prev) => ({ ...prev, slug }));
    }
  }, [formData.name, initialData?.slug]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tên thuộc tính là bắt buộc";
    }

    if (!formData.slug || !formData.slug.trim()) {
      newErrors.slug = "Slug là bắt buộc";
    }

    if (
      needsValues(formData.type) &&
      (!formData.values || formData.values.length === 0)
    ) {
      newErrors.values = "Loại thuộc tính này cần ít nhất một giá trị";
    }

    // Validate values
    if (formData.values) {
      formData.values.forEach((value, index) => {
        if (!value.value.trim()) {
          newErrors[`value_${index}`] = "Giá trị không được để trống";
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const needsValues = (type: AttributeType): boolean => {
    return ["COLOR", "SIZE", "SELECT", "MULTI_SELECT"].includes(type);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Vui lòng kiểm tra lại thông tin");
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Có lỗi xảy ra khi lưu thuộc tính");
    }
  };

  const addValue = () => {
    const newValue: AttributeValueFormData = {
      value: "",
      slug: "",
      sortOrder: (formData.values?.length || 0) + 1,
    };

    setFormData((prev) => ({
      ...prev,
      values: [...(prev.values || []), newValue],
    }));
  };

  const removeValue = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      values: prev.values?.filter((_, i) => i !== index) || [],
    }));
  };

  const updateValue = (
    index: number,
    field: keyof AttributeValueFormData,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      values:
        prev.values?.map((val, i) => {
          if (i === index) {
            const updated = { ...val, [field]: value };

            // Auto-generate slug for value
            if (field === "value" && typeof value === "string") {
              updated.slug = value
                .toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
                .replace(/[èéẹẻẽêềếệểễ]/g, "e")
                .replace(/[ìíịỉĩ]/g, "i")
                .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
                .replace(/[ùúụủũưừứựửữ]/g, "u")
                .replace(/[ỳýỵỷỹ]/g, "y")
                .replace(/[đ]/g, "d")
                .replace(/[^a-z0-9-]/g, "");
            }

            return updated;
          }
          return val;
        }) || [],
    }));
  };

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const values = [...(formData.values || [])];
    const draggedItem = values[draggedIndex];

    values.splice(draggedIndex, 1);
    values.splice(dropIndex, 0, draggedItem);

    // Update sort orders
    const updatedValues = values.map((value, index) => ({
      ...value,
      sortOrder: index + 1,
    }));

    setFormData((prev) => ({ ...prev, values: updatedValues }));
    setDraggedIndex(null);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Thông tin cơ bản</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                Tên thuộc tính <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Ví dụ: Màu sắc, Kích thước..."
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">
                Slug <span className="text-red-500">*</span>
              </Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, slug: e.target.value }))
                }
                placeholder="mau-sac, kich-thuoc..."
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.slug}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Mô tả chi tiết về thuộc tính này..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">
              Loại thuộc tính <span className="text-red-500">*</span>
            </Label>
            <Select
              value={formData.type}
              onValueChange={(value: AttributeType) => {
                setFormData((prev) => ({
                  ...prev,
                  type: value,
                  values: needsValues(value) ? prev.values : [],
                }));
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(ATTRIBUTE_TYPE_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    <div>
                      <div className="font-medium">{label}</div>
                      <div className="text-xs text-muted-foreground">
                        {ATTRIBUTE_TYPE_DESCRIPTIONS[value as AttributeType]}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="isRequired"
                checked={formData.isRequired}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({ ...prev, isRequired: checked }))
                }
              />
              <Label htmlFor="isRequired">Bắt buộc</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isFilterable"
                checked={formData.isFilterable}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({ ...prev, isFilterable: checked }))
                }
              />
              <Label htmlFor="isFilterable">Có thể lọc</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sortOrder">Thứ tự sắp xếp</Label>
              <Input
                id="sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    sortOrder: parseInt(e.target.value) || 0,
                  }))
                }
                min="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Values Section */}
      {needsValues(formData.type) && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Giá trị thuộc tính</CardTitle>
            <Button type="button" onClick={addValue} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Thêm giá trị
            </Button>
          </CardHeader>
          <CardContent>
            {errors.values && (
              <p className="text-sm text-red-500 flex items-center gap-1 mb-4">
                <AlertCircle className="h-4 w-4" />
                {errors.values}
              </p>
            )}

            <div className="space-y-3">
              {formData.values?.map((value, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
                  draggable
                  onDragStart={() => handleDragStart(index)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />

                  <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div>
                      <Input
                        value={value.value}
                        onChange={(e) =>
                          updateValue(index, "value", e.target.value)
                        }
                        placeholder="Giá trị"
                        className={
                          errors[`value_${index}`] ? "border-red-500" : ""
                        }
                      />
                      {errors[`value_${index}`] && (
                        <p className="text-xs text-red-500 mt-1">
                          {errors[`value_${index}`]}
                        </p>
                      )}
                    </div>

                    <Input
                      value={value.slug}
                      onChange={(e) =>
                        updateValue(index, "slug", e.target.value)
                      }
                      placeholder="Slug"
                    />

                    <Input
                      type="number"
                      value={value.sortOrder}
                      onChange={(e) =>
                        updateValue(
                          index,
                          "sortOrder",
                          parseInt(e.target.value) || 0
                        )
                      }
                      placeholder="Thứ tự"
                      min="0"
                    />
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeValue(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {(!formData.values || formData.values.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <p>Chưa có giá trị nào. Nhấn "Thêm giá trị" để bắt đầu.</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          <X className="h-4 w-4 mr-2" />
          Hủy
        </Button>
        <Button type="submit" disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading
            ? "Đang lưu..."
            : mode === "create"
              ? "Tạo thuộc tính"
              : "Cập nhật"}
        </Button>
      </div>
    </form>
  );
}
