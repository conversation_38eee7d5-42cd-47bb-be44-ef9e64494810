/**
 * MSW API handlers for testing
 * Mock API responses cho testing
 */

import { http, HttpResponse } from 'msw'
import { mockApiResponses, mockUsers, mockProducts, mockCategories, mockOrders } from '../fixtures/mock-data'

// Base URL for API
const API_BASE = 'http://localhost:3000/api'

export const handlers = [
  // Auth endpoints
  http.post(`${API_BASE}/auth/login`, async ({ request }) => {
    const body = await request.json() as { email: string; password: string }
    
    if (body.email === '<EMAIL>' && body.password === 'password123') {
      return HttpResponse.json(mockApiResponses.loginSuccess)
    }
    
    return HttpResponse.json(mockApiResponses.loginError, { status: 401 })
  }),

  http.post(`${API_BASE}/auth/register`, async ({ request }) => {
    const body = await request.json() as { email: string; name: string; password: string }
    
    return HttpResponse.json({
      success: true,
      message: '<PERSON><PERSON><PERSON> ký thành công',
      user: {
        id: 'new-user-id',
        email: body.email,
        name: body.name,
        role: 'USER',
      },
    })
  }),

  http.post(`${API_BASE}/auth/logout`, () => {
    return HttpResponse.json({
      success: true,
      message: 'Đăng xuất thành công',
    })
  }),

  // User endpoints
  http.get(`${API_BASE}/users/profile`, () => {
    return HttpResponse.json({
      user: mockUsers[0],
    })
  }),

  http.put(`${API_BASE}/users/profile`, async ({ request }) => {
    const body = await request.json() as Partial<typeof mockUsers[0]>
    
    return HttpResponse.json({
      success: true,
      user: { ...mockUsers[0], ...body },
    })
  }),

  // Product endpoints
  http.get(`${API_BASE}/products`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const search = url.searchParams.get('search')
    const category = url.searchParams.get('category')

    let filteredProducts = [...mockProducts]

    if (search) {
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.description.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (category) {
      filteredProducts = filteredProducts.filter(product =>
        product.categoryId === category
      )
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex)

    return HttpResponse.json({
      products: paginatedProducts,
      total: filteredProducts.length,
      page,
      limit,
      totalPages: Math.ceil(filteredProducts.length / limit),
    })
  }),

  http.get(`${API_BASE}/products/:id`, ({ params }) => {
    const product = mockProducts.find(p => p.id === params.id)
    
    if (!product) {
      return HttpResponse.json({ error: 'Sản phẩm không tồn tại' }, { status: 404 })
    }
    
    return HttpResponse.json({ product })
  }),

  http.post(`${API_BASE}/products`, async ({ request }) => {
    const body = await request.json() as any
    
    const newProduct = {
      id: `prod-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...body,
    }
    
    return HttpResponse.json({
      success: true,
      product: newProduct,
    }, { status: 201 })
  }),

  http.put(`${API_BASE}/products/:id`, async ({ params, request }) => {
    const body = await request.json() as any
    const product = mockProducts.find(p => p.id === params.id)
    
    if (!product) {
      return HttpResponse.json({ error: 'Sản phẩm không tồn tại' }, { status: 404 })
    }
    
    const updatedProduct = {
      ...product,
      ...body,
      updatedAt: new Date(),
    }
    
    return HttpResponse.json({
      success: true,
      product: updatedProduct,
    })
  }),

  http.delete(`${API_BASE}/products/:id`, ({ params }) => {
    const product = mockProducts.find(p => p.id === params.id)
    
    if (!product) {
      return HttpResponse.json({ error: 'Sản phẩm không tồn tại' }, { status: 404 })
    }
    
    return HttpResponse.json({
      success: true,
      message: 'Xóa sản phẩm thành công',
    })
  }),

  // Category endpoints
  http.get(`${API_BASE}/categories`, () => {
    return HttpResponse.json({
      categories: mockCategories,
    })
  }),

  http.get(`${API_BASE}/categories/:id`, ({ params }) => {
    const category = mockCategories.find(c => c.id === params.id)
    
    if (!category) {
      return HttpResponse.json({ error: 'Danh mục không tồn tại' }, { status: 404 })
    }
    
    return HttpResponse.json({ category })
  }),

  // Order endpoints
  http.get(`${API_BASE}/orders`, ({ request }) => {
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    
    let filteredOrders = [...mockOrders]
    
    if (userId) {
      filteredOrders = filteredOrders.filter(order => order.userId === userId)
    }
    
    return HttpResponse.json({
      orders: filteredOrders,
      total: filteredOrders.length,
    })
  }),

  http.get(`${API_BASE}/orders/:id`, ({ params }) => {
    const order = mockOrders.find(o => o.id === params.id)
    
    if (!order) {
      return HttpResponse.json({ error: 'Đơn hàng không tồn tại' }, { status: 404 })
    }
    
    return HttpResponse.json({ order })
  }),

  http.post(`${API_BASE}/orders`, async ({ request }) => {
    const body = await request.json() as any
    
    const newOrder = {
      id: `order-${Date.now()}`,
      status: 'PENDING',
      paymentStatus: 'PENDING',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...body,
    }
    
    return HttpResponse.json({
      success: true,
      order: newOrder,
    }, { status: 201 })
  }),

  // Cart endpoints
  http.get(`${API_BASE}/cart`, () => {
    return HttpResponse.json(mockApiResponses.cartSuccess)
  }),

  http.post(`${API_BASE}/cart/items`, async ({ request }) => {
    const body = await request.json() as { productId: string; quantity: number }
    
    return HttpResponse.json({
      success: true,
      message: 'Đã thêm sản phẩm vào giỏ hàng',
      item: {
        id: `cart-item-${Date.now()}`,
        productId: body.productId,
        quantity: body.quantity,
        createdAt: new Date(),
      },
    })
  }),

  http.put(`${API_BASE}/cart/items/:id`, async ({ params, request }) => {
    const body = await request.json() as { quantity: number }
    
    return HttpResponse.json({
      success: true,
      message: 'Đã cập nhật số lượng sản phẩm',
      item: {
        id: params.id,
        quantity: body.quantity,
        updatedAt: new Date(),
      },
    })
  }),

  http.delete(`${API_BASE}/cart/items/:id`, ({ params }) => {
    return HttpResponse.json({
      success: true,
      message: 'Đã xóa sản phẩm khỏi giỏ hàng',
    })
  }),

  // Error simulation endpoints
  http.get(`${API_BASE}/test/error`, () => {
    return HttpResponse.json({ error: 'Test error' }, { status: 500 })
  }),

  http.get(`${API_BASE}/test/timeout`, () => {
    return new Promise(() => {
      // Never resolves to simulate timeout
    })
  }),
]
