import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

const createAuditLogSchema = z.object({
  action: z.string().min(1, "Hành động là bắt buộc"),
  resource: z.string().min(1, "Tài nguyên là bắt buộc"),
  resourceId: z.string().optional(),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  description: z.string().optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

// GET /api/admin/logs - Get audit logs
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can view audit logs
    if (adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xem logs" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search");
    const action = searchParams.get("action");
    const resource = searchParams.get("resource");
    const adminId = searchParams.get("adminId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { action: { contains: search, mode: "insensitive" } },
        { resource: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { resourceId: { contains: search, mode: "insensitive" } },
      ];
    }

    if (action) {
      where.action = action;
    }

    if (resource) {
      where.resource = resource;
    }

    if (adminId) {
      where.adminId = adminId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          admin: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    // Get summary statistics
    const stats = await prisma.auditLog.groupBy({
      by: ["action"],
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      _count: {
        action: true,
      },
    });

    const resourceStats = await prisma.auditLog.groupBy({
      by: ["resource"],
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      _count: {
        resource: true,
      },
    });

    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats: {
        actions: stats.map((s) => ({
          action: s.action,
          count: s._count.action,
        })),
        resources: resourceStats.map((s) => ({
          resource: s.resource,
          count: s._count.resource,
        })),
      },
    });
  } catch (error) {
    console.error("Get audit logs error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách logs" },
      { status: 500 }
    );
  }
}

// POST /api/admin/logs - Create audit log entry
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = createAuditLogSchema.parse(body);

    // Get IP address and user agent from request
    const ipAddress =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      data.ipAddress ||
      "unknown";

    const userAgent =
      request.headers.get("user-agent") || data.userAgent || "unknown";

    const auditLog = await prisma.auditLog.create({
      data: {
        action: data.action,
        resource: data.resource,
        resourceId: data.resourceId,
        oldValues: data.oldValues,
        newValues: data.newValues,
        description: data.description,
        ipAddress,
        userAgent,
        adminId: adminToken.id as string,
      },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: "Ghi log thành công",
        auditLog,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create audit log error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi ghi log" },
      { status: 500 }
    );
  }
}
