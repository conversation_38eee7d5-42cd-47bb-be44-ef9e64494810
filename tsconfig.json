{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": ["jest", "@testing-library/jest-dom", "node"]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "src/middleware.ts", ".next/types/**/*.ts", "tests/types/**/*.d.ts", "tests/jest-dom.d.ts"], "exclude": ["node_modules", ".next"]}