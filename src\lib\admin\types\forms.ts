import { ReactNode } from 'react';

// Form Field Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date' | 'datetime-local';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ value: string; label: string }>;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: any) => boolean | string;
  };
  defaultValue?: any;
  description?: string;
  className?: string;
}

// Form Configuration
export interface FormConfig {
  fields: FormField[];
  submitText?: string;
  cancelText?: string;
  layout?: 'vertical' | 'horizontal' | 'grid';
  columns?: number;
  onSubmit: (data: any) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
  initialData?: any;
}

// Form State
export interface FormState {
  data: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  loading: boolean;
  submitting: boolean;
}

// Form Actions
export type FormAction = 
  | { type: 'SET_FIELD'; field: string; value: any }
  | { type: 'SET_ERROR'; field: string; error: string }
  | { type: 'CLEAR_ERROR'; field: string }
  | { type: 'SET_TOUCHED'; field: string }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_SUBMITTING'; submitting: boolean }
  | { type: 'RESET_FORM'; initialData?: any }
  | { type: 'SET_ERRORS'; errors: Record<string, string> };

// Form Hook Return Type
export interface UseFormReturn {
  state: FormState;
  setValue: (field: string, value: any) => void;
  setError: (field: string, error: string) => void;
  clearError: (field: string) => void;
  setTouched: (field: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  reset: (initialData?: any) => void;
  validate: () => boolean;
  isValid: boolean;
  isDirty: boolean;
}

// Admin Form Props
export interface AdminFormProps {
  config: FormConfig;
  className?: string;
  children?: ReactNode;
}

// Specific Form Types for Admin Entities
export interface ProductFormData {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  images: string[];
  categoryId: string;
  stock: number;
  sku: string;
  featured: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
  tags: string[];
  slug?: string;
}

export interface CategoryFormData {
  name: string;
  description?: string;
  slug: string;
  parentId?: string;
  image?: string;
}

export interface UserFormData {
  name: string;
  email: string;
  phone?: string;
  role: 'ADMIN' | 'USER';
  avatar?: string;
}

export interface OrderFormData {
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
  shippingAddress?: {
    fullName: string;
    phone: string;
    address: string;
    city: string;
    district: string;
    ward: string;
  };
  notes?: string;
}

export interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string;
}

export interface SettingsFormData {
  [key: string]: any;
}
