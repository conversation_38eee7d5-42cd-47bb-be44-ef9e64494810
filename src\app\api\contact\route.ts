import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { ContactFormEmailData } from "@/lib/email/templates";

const contactFormSchema = z.object({
  name: z.string().min(2, "Tên phải có ít nhất 2 ký tự"),
  email: z.string().email("Email không hợp lệ"),
  phone: z.string().optional(),
  subject: z.string().min(5, "Chủ đề phải có ít nhất 5 ký tự"),
  message: z.string().min(10, "Tin nhắn phải có ít nhất 10 ký tự"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = contactFormSchema.parse(body);

    // Initialize email service
    const initialized = await emailService.initialize();
    if (!initialized) {
      return NextResponse.json(
        { error: "Dị<PERSON> vụ email chưa đ<PERSON> c<PERSON> hình" },
        { status: 503 }
      );
    }

    // Prepare contact form email data
    const contactFormData: ContactFormEmailData = {
      recipientName: "NS Shop Admin",
      recipientEmail:
        process.env.ADMIN_EMAIL || process.env.EMAIL_FROM || "<EMAIL>",
      senderName: data.name,
      senderEmail: data.email,
      senderPhone: data.phone,
      subject: data.subject,
      message: data.message,
      submittedAt: new Date().toISOString(),
    };

    // Send contact form email
    const emailSent = await emailService.sendContactFormEmail(contactFormData);

    if (!emailSent) {
      return NextResponse.json(
        { error: "Không thể gửi email. Vui lòng thử lại sau." },
        { status: 500 }
      );
    }

    // Optionally, you can also save the contact form submission to database
    // await prisma.contactSubmission.create({ data: ... });

    return NextResponse.json(
      {
        message:
          "Gửi tin nhắn thành công! Chúng tôi sẽ phản hồi trong thời gian sớm nhất.",
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Contact form error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi gửi tin nhắn" },
      { status: 500 }
    );
  }
}
