'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Head<PERSON>, Footer } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCart } from '@/contexts/cart-context';
import { 
	ArrowLeft, 
	CreditCard, 
	Truck, 
	MapPin,
	Phone,
	User,
	Building
} from 'lucide-react';
import { toast } from 'sonner';

interface ShippingAddress {
	fullName: string;
	phone: string;
	address: string;
	ward: string;
	district: string;
	province: string;
}

export default function CheckoutPage() {
	const { data: session } = useSession();
	const router = useRouter();
	const { state } = useCart();
	const { cart } = state;

	const [loading, setLoading] = useState(false);
	const [step, setStep] = useState(1); // 1: Shipping, 2: Payment, 3: Review
	const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
		fullName: '',
		phone: '',
		address: '',
		ward: '',
		district: '',
		province: '',
	});
	const [billingAddress, setBillingAddress] = useState<ShippingAddress>({
		fullName: '',
		phone: '',
		address: '',
		ward: '',
		district: '',
		province: '',
	});
	const [useSameAddress, setUseSameAddress] = useState(true);
	const [paymentMethod, setPaymentMethod] = useState<'COD' | 'BANK_TRANSFER' | 'CREDIT_CARD'>('COD');
	const [notes, setNotes] = useState('');

	useEffect(() => {
		if (!session) {
			router.push('/auth/signin');
			return;
		}

		if (!cart || cart.items.length === 0) {
			router.push('/cart');
			return;
		}

		// Pre-fill user info if available
		if (session.user) {
			setShippingAddress(prev => ({
				...prev,
				fullName: session.user.name || '',
			}));
			setBillingAddress(prev => ({
				...prev,
				fullName: session.user.name || '',
			}));
		}
	}, [session, cart, router]);

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND',
		}).format(price);
	};

	const handleShippingSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		
		// Validate shipping address
		const requiredFields = ['fullName', 'phone', 'address', 'ward', 'district', 'province'];
		const missingFields = requiredFields.filter(field => !shippingAddress[field as keyof ShippingAddress]);
		
		if (missingFields.length > 0) {
			toast.error('Vui lòng điền đầy đủ thông tin giao hàng');
			return;
		}

		if (useSameAddress) {
			setBillingAddress(shippingAddress);
		}

		setStep(2);
	};

	const handlePaymentSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!useSameAddress) {
			// Validate billing address
			const requiredFields = ['fullName', 'phone', 'address', 'ward', 'district', 'province'];
			const missingFields = requiredFields.filter(field => !billingAddress[field as keyof ShippingAddress]);
			
			if (missingFields.length > 0) {
				toast.error('Vui lòng điền đầy đủ thông tin thanh toán');
				return;
			}
		}

		setStep(3);
	};

	const handlePlaceOrder = async () => {
		setLoading(true);

		try {
			const orderData = {
				shippingAddress,
				billingAddress: useSameAddress ? shippingAddress : billingAddress,
				paymentMethod,
				notes: notes.trim() || undefined,
			};

			const response = await fetch('/api/orders', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(orderData),
			});

			const data = await response.json();

			if (response.ok) {
				toast.success('Đặt hàng thành công!');
				router.push(`/orders/${data.order.id}`);
			} else {
				toast.error(data.error || 'Có lỗi xảy ra khi đặt hàng');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi đặt hàng');
		} finally {
			setLoading(false);
		}
	};

	if (!session || !cart) {
		return null;
	}

	return (
		<div className="min-h-screen flex flex-col">
			<Header />

			<main className="flex-1 container mx-auto px-4 py-8">
				{/* Header */}
				<div className="flex items-center gap-4 mb-8">
					<Link
						href="/cart"
						className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
					>
						<ArrowLeft className="h-4 w-4" />
						Quay lại giỏ hàng
					</Link>
				</div>

				<div className="mb-8">
					<h1 className="text-3xl font-bold mb-2">Thanh toán</h1>
					<p className="text-muted-foreground">
						Hoàn tất đơn hàng của bạn
					</p>
				</div>

				{/* Progress Steps */}
				<div className="flex items-center justify-center mb-8">
					<div className="flex items-center space-x-4">
						{[
							{ step: 1, title: 'Giao hàng', icon: Truck },
							{ step: 2, title: 'Thanh toán', icon: CreditCard },
							{ step: 3, title: 'Xác nhận', icon: MapPin },
						].map(({ step: stepNum, title, icon: Icon }) => (
							<div key={stepNum} className="flex items-center">
								<div
									className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
										step >= stepNum
											? 'bg-pink-600 border-pink-600 text-white'
											: 'border-gray-300 text-gray-400'
									}`}
								>
									<Icon className="h-4 w-4" />
								</div>
								<span
									className={`ml-2 text-sm font-medium ${
										step >= stepNum ? 'text-pink-600' : 'text-gray-400'
									}`}
								>
									{title}
								</span>
								{stepNum < 3 && (
									<div
										className={`w-16 h-0.5 ml-4 ${
											step > stepNum ? 'bg-pink-600' : 'bg-gray-300'
										}`}
									/>
								)}
							</div>
						))}
					</div>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					{/* Main Content */}
					<div className="lg:col-span-2">
						{/* Step 1: Shipping Information */}
						{step === 1 && (
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<Truck className="h-5 w-5" />
										Thông tin giao hàng
									</CardTitle>
								</CardHeader>
								<CardContent>
									<form onSubmit={handleShippingSubmit} className="space-y-4">
										<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
											<div>
												<label className="block text-sm font-medium mb-2">
													Họ và tên *
												</label>
												<div className="relative">
													<User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<input
														type="text"
														value={shippingAddress.fullName}
														onChange={(e) =>
															setShippingAddress({
																...shippingAddress,
																fullName: e.target.value,
															})
														}
														className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
														required
													/>
												</div>
											</div>
											<div>
												<label className="block text-sm font-medium mb-2">
													Số điện thoại *
												</label>
												<div className="relative">
													<Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<input
														type="tel"
														value={shippingAddress.phone}
														onChange={(e) =>
															setShippingAddress({
																...shippingAddress,
																phone: e.target.value,
															})
														}
														className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
														required
													/>
												</div>
											</div>
										</div>

										<div>
											<label className="block text-sm font-medium mb-2">
												Địa chỉ *
											</label>
											<div className="relative">
												<MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
												<textarea
													value={shippingAddress.address}
													onChange={(e) =>
														setShippingAddress({
															...shippingAddress,
															address: e.target.value,
														})
													}
													rows={2}
													className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
													required
												/>
											</div>
										</div>

										<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
											<div>
												<label className="block text-sm font-medium mb-2">
													Phường/Xã *
												</label>
												<input
													type="text"
													value={shippingAddress.ward}
													onChange={(e) =>
														setShippingAddress({
															...shippingAddress,
															ward: e.target.value,
														})
													}
													className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
													required
												/>
											</div>
											<div>
												<label className="block text-sm font-medium mb-2">
													Quận/Huyện *
												</label>
												<input
													type="text"
													value={shippingAddress.district}
													onChange={(e) =>
														setShippingAddress({
															...shippingAddress,
															district: e.target.value,
														})
													}
													className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
													required
												/>
											</div>
											<div>
												<label className="block text-sm font-medium mb-2">
													Tỉnh/Thành phố *
												</label>
												<input
													type="text"
													value={shippingAddress.province}
													onChange={(e) =>
														setShippingAddress({
															...shippingAddress,
															province: e.target.value,
														})
													}
													className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
													required
												/>
											</div>
										</div>

										<Button
											type="submit"
											className="w-full bg-pink-600 hover:bg-pink-700"
										>
											Tiếp tục đến thanh toán
										</Button>
									</form>
								</CardContent>
							</Card>
						)}

						{/* Step 2: Payment Information */}
						{step === 2 && (
							<div className="space-y-6">
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<CreditCard className="h-5 w-5" />
											Phương thức thanh toán
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										{[
											{
												id: 'COD',
												title: 'Thanh toán khi nhận hàng (COD)',
												description: 'Thanh toán bằng tiền mặt khi nhận hàng',
											},
											{
												id: 'BANK_TRANSFER',
												title: 'Chuyển khoản ngân hàng',
												description: 'Chuyển khoản qua ngân hàng hoặc ví điện tử',
											},
											{
												id: 'CREDIT_CARD',
												title: 'Thẻ tín dụng/Ghi nợ',
												description: 'Thanh toán bằng thẻ Visa, Mastercard',
											},
										].map((method) => (
											<label
												key={method.id}
												className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors ${
													paymentMethod === method.id
														? 'border-pink-500 bg-pink-50'
														: 'border-gray-200 hover:border-gray-300'
												}`}
											>
												<input
													type="radio"
													name="paymentMethod"
													value={method.id}
													checked={paymentMethod === method.id}
													onChange={(e) =>
														setPaymentMethod(e.target.value as any)
													}
													className="mt-1"
												/>
												<div>
													<div className="font-medium">{method.title}</div>
													<div className="text-sm text-muted-foreground">
														{method.description}
													</div>
												</div>
											</label>
										))}
									</div>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<Building className="h-5 w-5" />
										Thông tin thanh toán
									</CardTitle>
								</CardHeader>
								<CardContent>
									<form onSubmit={handlePaymentSubmit} className="space-y-4">
										<div className="flex items-center space-x-2">
											<input
												type="checkbox"
												id="sameAddress"
												checked={useSameAddress}
												onChange={(e) => setUseSameAddress(e.target.checked)}
												className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
											/>
											<label htmlFor="sameAddress" className="text-sm">
												Sử dụng địa chỉ giao hàng làm địa chỉ thanh toán
											</label>
										</div>

										{!useSameAddress && (
											<div className="space-y-4 pt-4 border-t">
												<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
													<div>
														<label className="block text-sm font-medium mb-2">
															Họ và tên *
														</label>
														<input
															type="text"
															value={billingAddress.fullName}
															onChange={(e) =>
																setBillingAddress({
																	...billingAddress,
																	fullName: e.target.value,
																})
															}
															className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
															required
														/>
													</div>
													<div>
														<label className="block text-sm font-medium mb-2">
															Số điện thoại *
														</label>
														<input
															type="tel"
															value={billingAddress.phone}
															onChange={(e) =>
																setBillingAddress({
																	...billingAddress,
																	phone: e.target.value,
																})
															}
															className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
															required
														/>
													</div>
												</div>
												{/* Add more billing address fields similar to shipping */}
											</div>
										)}

										<div>
											<label className="block text-sm font-medium mb-2">
												Ghi chú đơn hàng (tùy chọn)
											</label>
											<textarea
												value={notes}
												onChange={(e) => setNotes(e.target.value)}
												rows={3}
												placeholder="Ghi chú về đơn hàng, ví dụ: giao hàng vào buổi chiều..."
												className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
											/>
										</div>

										<div className="flex gap-2">
											<Button
												type="button"
												variant="outline"
												onClick={() => setStep(1)}
												className="flex-1"
											>
												Quay lại
											</Button>
											<Button
												type="submit"
												className="flex-1 bg-pink-600 hover:bg-pink-700"
											>
												Xem lại đơn hàng
											</Button>
										</div>
									</form>
								</CardContent>
							</Card>
						</div>
						)}

						{/* Step 3: Order Review */}
						{step === 3 && (
							<Card>
								<CardHeader>
									<CardTitle>Xác nhận đơn hàng</CardTitle>
								</CardHeader>
								<CardContent className="space-y-6">
									{/* Order Items */}
									<div>
										<h3 className="font-semibold mb-4">Sản phẩm đã đặt</h3>
										<div className="space-y-4">
											{cart.items.map((item) => (
												<div key={item.id} className="flex gap-4">
													<div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100">
														<Image
															src={item.product.images[0] || '/images/placeholder.jpg'}
															alt={item.product.name}
															fill
															className="object-cover"
														/>
													</div>
													<div className="flex-1">
														<h4 className="font-medium text-sm">{item.product.name}</h4>
														<p className="text-sm text-muted-foreground">
															Số lượng: {item.quantity}
														</p>
														<p className="font-semibold">
															{formatPrice(
																(item.product.salePrice || item.product.price) * item.quantity
															)}
														</p>
													</div>
												</div>
											))}
										</div>
									</div>

									{/* Addresses */}
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div>
											<h3 className="font-semibold mb-2">Địa chỉ giao hàng</h3>
											<div className="text-sm space-y-1">
												<p className="font-medium">{shippingAddress.fullName}</p>
												<p>{shippingAddress.phone}</p>
												<p>{shippingAddress.address}</p>
												<p>
													{shippingAddress.ward}, {shippingAddress.district}, {shippingAddress.province}
												</p>
											</div>
										</div>
										<div>
											<h3 className="font-semibold mb-2">Phương thức thanh toán</h3>
											<p className="text-sm">
												{paymentMethod === 'COD' && 'Thanh toán khi nhận hàng'}
												{paymentMethod === 'BANK_TRANSFER' && 'Chuyển khoản ngân hàng'}
												{paymentMethod === 'CREDIT_CARD' && 'Thẻ tín dụng/Ghi nợ'}
											</p>
										</div>
									</div>

									{notes && (
										<div>
											<h3 className="font-semibold mb-2">Ghi chú</h3>
											<p className="text-sm text-muted-foreground">{notes}</p>
										</div>
									)}

									<div className="flex gap-2">
										<Button
											type="button"
											variant="outline"
											onClick={() => setStep(2)}
											className="flex-1"
										>
											Quay lại
										</Button>
										<Button
											onClick={handlePlaceOrder}
											disabled={loading}
											className="flex-1 bg-pink-600 hover:bg-pink-700"
										>
											{loading ? 'Đang xử lý...' : 'Đặt hàng'}
										</Button>
									</div>
								</CardContent>
							</Card>
						)}
					</div>

					{/* Order Summary */}
					<div>
						<Card className="sticky top-4">
							<CardHeader>
								<CardTitle>Tóm tắt đơn hàng</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="space-y-2">
									<div className="flex justify-between text-sm">
										<span>Tạm tính ({cart.itemCount} sản phẩm)</span>
										<span>{formatPrice(cart.subtotal)}</span>
									</div>
									<div className="flex justify-between text-sm">
										<span>Phí vận chuyển</span>
										<span className="text-green-600">Miễn phí</span>
									</div>
									<div className="border-t pt-2">
										<div className="flex justify-between font-semibold">
											<span>Tổng cộng</span>
											<span className="text-pink-600">
												{formatPrice(cart.subtotal)}
											</span>
										</div>
									</div>
								</div>

								{/* Order Items Preview */}
								<div className="pt-4 border-t">
									<h4 className="font-medium mb-3">Sản phẩm ({cart.items.length})</h4>
									<div className="space-y-2 max-h-48 overflow-y-auto">
										{cart.items.map((item) => (
											<div key={item.id} className="flex gap-2 text-sm">
												<div className="relative w-10 h-10 flex-shrink-0 overflow-hidden rounded bg-gray-100">
													<Image
														src={item.product.images[0] || '/images/placeholder.jpg'}
														alt={item.product.name}
														fill
														className="object-cover"
													/>
												</div>
												<div className="flex-1 min-w-0">
													<p className="font-medium truncate">{item.product.name}</p>
													<p className="text-muted-foreground">
														{item.quantity} x {formatPrice(item.product.salePrice || item.product.price)}
													</p>
												</div>
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</main>

			<Footer />
		</div>
	);
}
