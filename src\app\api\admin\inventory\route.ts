import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for inventory update
const inventoryUpdateSchema = z.object({
  quantity: z.number().min(0, "Số lượng phải >= 0"),
  minStock: z.number().min(0, "<PERSON><PERSON><PERSON> tồn kho tối thiểu phải >= 0"),
  maxStock: z.number().min(0, "<PERSON><PERSON><PERSON> tồn kho tối đa phải >= 0").optional(),
  location: z.string().optional(),
});

// GET /api/admin/inventory - Get inventory entries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const location = searchParams.get("location") || "";
    const lowStock = searchParams.get("lowStock") === "true";
    const outOfStock = searchParams.get("outOfStock") === "true";
    const brandId = searchParams.get("brandId") || "";
    const categoryId = searchParams.get("categoryId") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.product = {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
        ],
      };
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (brandId) {
      where.product = {
        ...where.product,
        brandId,
      };
    }

    if (categoryId) {
      where.product = {
        ...where.product,
        categoryId,
      };
    }

    if (lowStock) {
      where.available = { lte: prisma.inventoryEntry.fields.minStock };
    }

    if (outOfStock) {
      where.available = { lte: 0 };
    }

    // Get total count
    const total = await prisma.inventoryEntry.count({ where });

    // Get inventory entries with pagination
    const inventoryEntries = await prisma.inventoryEntry.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        product: {
          include: {
            category: true,
            brand: true,
          },
        },
        stockMovements: {
          take: 5,
          orderBy: { createdAt: "desc" },
        },
      },
    });

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: inventoryEntries,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Get inventory error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách kho hàng" },
      { status: 500 }
    );
  }
}

// POST /api/admin/inventory - Create inventory entry for product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { productId, ...inventoryData } = body;
    
    // Validate input
    const validatedData = inventoryUpdateSchema.parse(inventoryData);

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    // Check if inventory entry already exists
    const existingEntry = await prisma.inventoryEntry.findUnique({
      where: { productId },
    });

    if (existingEntry) {
      return NextResponse.json(
        { success: false, error: "Sản phẩm đã có thông tin kho hàng" },
        { status: 400 }
      );
    }

    // Create inventory entry
    const inventoryEntry = await prisma.inventoryEntry.create({
      data: {
        productId,
        ...validatedData,
        available: validatedData.quantity, // Initially all quantity is available
      },
      include: {
        product: {
          include: {
            category: true,
            brand: true,
          },
        },
      },
    });

    // Create initial stock movement
    await prisma.stockMovement.create({
      data: {
        inventoryEntryId: inventoryEntry.id,
        type: "IN",
        quantity: validatedData.quantity,
        reason: "Khởi tạo kho hàng",
        notes: "Tạo mới thông tin kho hàng cho sản phẩm",
      },
    });

    return NextResponse.json({
      success: true,
      data: inventoryEntry,
      message: "Thông tin kho hàng đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create inventory error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo thông tin kho hàng" },
      { status: 500 }
    );
  }
}
