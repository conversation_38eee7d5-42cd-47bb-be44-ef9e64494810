"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Palette,
  Ruler,
  Tag,
  Info,
  Check,
  X,
  Star,
  Package,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Attribute,
  AttributeValue,
  ProductAttribute,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

interface AttributeDisplayProps {
  attributes: ProductAttribute[];
  layout?: "grid" | "list" | "compact";
  showIcons?: boolean;
  showDescriptions?: boolean;
  className?: string;
}

interface AttributeItemProps {
  productAttribute: ProductAttribute;
  layout?: "grid" | "list" | "compact";
  showIcon?: boolean;
  showDescription?: boolean;
}

function AttributeItem({
  productAttribute,
  layout = "list",
  showIcon = true,
  showDescription = false,
}: AttributeItemProps) {
  const { attribute, attributeValue } = productAttribute;

  if (!attribute || !attributeValue) return null;

  const getAttributeIcon = (type: string) => {
    switch (type) {
      case "COLOR":
        return <Palette className="h-4 w-4" />;
      case "SIZE":
        return <Ruler className="h-4 w-4" />;
      case "BOOLEAN":
        return attributeValue.value === "true" ? (
          <Check className="h-4 w-4 text-green-600" />
        ) : (
          <X className="h-4 w-4 text-red-600" />
        );
      default:
        return <Tag className="h-4 w-4" />;
    }
  };

  const getColorCode = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      "đỏ": "#ef4444",
      "xanh dương": "#3b82f6",
      "xanh lá": "#22c55e",
      "vàng": "#eab308",
      "đen": "#000000",
      "trắng": "#ffffff",
      "hồng": "#ec4899",
      "tím": "#a855f7",
      "xám": "#6b7280",
      "nâu": "#a3a3a3",
      "cam": "#f97316",
      "be": "#f5f5dc",
      "navy": "#1e3a8a",
      "khaki": "#f0e68c",
    };
    return colorMap[colorName.toLowerCase()] || "#6b7280";
  };

  const renderValue = () => {
    switch (attribute.type) {
      case "COLOR":
        return (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-gray-300"
              style={{
                backgroundColor: getColorCode(attributeValue.value),
              }}
            />
            <span>{attributeValue.value}</span>
          </div>
        );

      case "SIZE":
        return (
          <Badge variant="outline" className="font-medium">
            {attributeValue.value}
          </Badge>
        );

      case "BOOLEAN":
        return (
          <Badge
            variant={attributeValue.value === "true" ? "default" : "secondary"}
            className={cn(
              attributeValue.value === "true"
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            )}
          >
            {attributeValue.value === "true" ? "Có" : "Không"}
          </Badge>
        );

      case "NUMBER":
        return (
          <Badge variant="outline" className="font-mono">
            {attributeValue.value}
          </Badge>
        );

      default:
        return <Badge variant="outline">{attributeValue.value}</Badge>;
    }
  };

  const getBadgeVariant = () => {
    if (attribute.isRequired) return "default";
    return "secondary";
  };

  if (layout === "compact") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1">
              {showIcon && getAttributeIcon(attribute.type)}
              {renderValue()}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{attribute.name}</p>
              {attribute.description && (
                <p className="text-sm text-muted-foreground">
                  {attribute.description}
                </p>
              )}
              <p className="text-xs">
                Loại: {ATTRIBUTE_TYPE_LABELS[attribute.type]}
              </p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (layout === "grid") {
    return (
      <Card className="p-3">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {showIcon && getAttributeIcon(attribute.type)}
            <span className="font-medium text-sm">{attribute.name}</span>
            {attribute.isRequired && (
              <Badge variant="destructive" className="text-xs px-1 py-0">
                Bắt buộc
              </Badge>
            )}
          </div>
          <div>{renderValue()}</div>
          {showDescription && attribute.description && (
            <p className="text-xs text-muted-foreground">
              {attribute.description}
            </p>
          )}
        </div>
      </Card>
    );
  }

  // Default list layout
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center gap-2">
        {showIcon && getAttributeIcon(attribute.type)}
        <div>
          <span className="font-medium">{attribute.name}</span>
          {attribute.isRequired && (
            <Badge variant="destructive" className="ml-2 text-xs px-1 py-0">
              Bắt buộc
            </Badge>
          )}
          {showDescription && attribute.description && (
            <p className="text-sm text-muted-foreground">
              {attribute.description}
            </p>
          )}
        </div>
      </div>
      <div>{renderValue()}</div>
    </div>
  );
}

export function AttributeDisplay({
  attributes,
  layout = "list",
  showIcons = true,
  showDescriptions = false,
  className,
}: AttributeDisplayProps) {
  if (!attributes || attributes.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium mb-2">Chưa có thuộc tính</p>
          <p className="text-sm text-muted-foreground">
            Sản phẩm này chưa được gán thuộc tính nào
          </p>
        </CardContent>
      </Card>
    );
  }

  const requiredAttributes = attributes.filter(
    (attr) => attr.attribute?.isRequired
  );
  const optionalAttributes = attributes.filter(
    (attr) => !attr.attribute?.isRequired
  );

  const renderAttributeGroup = (
    groupAttributes: ProductAttribute[],
    title: string,
    icon?: React.ReactNode
  ) => {
    if (groupAttributes.length === 0) return null;

    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          {icon}
          <h4 className="font-medium text-sm">{title}</h4>
          <Badge variant="outline" className="text-xs">
            {groupAttributes.length}
          </Badge>
        </div>
        
        {layout === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {groupAttributes.map((attr) => (
              <AttributeItem
                key={attr.id}
                productAttribute={attr}
                layout={layout}
                showIcon={showIcons}
                showDescription={showDescriptions}
              />
            ))}
          </div>
        ) : layout === "compact" ? (
          <div className="flex flex-wrap gap-2">
            {groupAttributes.map((attr) => (
              <AttributeItem
                key={attr.id}
                productAttribute={attr}
                layout={layout}
                showIcon={showIcons}
                showDescription={showDescriptions}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-1">
            {groupAttributes.map((attr, index) => (
              <div key={attr.id}>
                <AttributeItem
                  productAttribute={attr}
                  layout={layout}
                  showIcon={showIcons}
                  showDescription={showDescriptions}
                />
                {index < groupAttributes.length - 1 && <Separator />}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tag className="h-5 w-5" />
          Thuộc tính sản phẩm
          <Badge variant="outline">{attributes.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Attributes */}
        {renderAttributeGroup(
          requiredAttributes,
          "Thuộc tính bắt buộc",
          <Star className="h-4 w-4 text-red-500" />
        )}

        {/* Separator between groups */}
        {requiredAttributes.length > 0 && optionalAttributes.length > 0 && (
          <Separator />
        )}

        {/* Optional Attributes */}
        {renderAttributeGroup(
          optionalAttributes,
          "Thuộc tính tùy chọn",
          <Info className="h-4 w-4 text-blue-500" />
        )}

        {/* Summary */}
        {layout !== "compact" && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Tổng cộng: {attributes.length} thuộc tính</span>
              <div className="flex items-center gap-4">
                {requiredAttributes.length > 0 && (
                  <span>Bắt buộc: {requiredAttributes.length}</span>
                )}
                {optionalAttributes.length > 0 && (
                  <span>Tùy chọn: {optionalAttributes.length}</span>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
