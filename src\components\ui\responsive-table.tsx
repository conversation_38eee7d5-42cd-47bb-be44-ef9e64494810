"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, ChevronDown, ChevronRight } from "lucide-react";

interface ResponsiveTableColumn<T = any> {
  key: string;
  title: string;
  sortable?: boolean;
  className?: string;
  width?: string | number;
  align?: "left" | "center" | "right";
  render?: (record: T, index: number) => React.ReactNode;
  mobileRender?: (record: T, index: number) => React.ReactNode;
  hideOnMobile?: boolean;
  priority?: number; // 1 = highest priority (always show), 5 = lowest priority (hide first)
}

interface ResponsiveTableAction<T = any> {
  key: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (record: T) => void;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  disabled?: (record: T) => boolean;
}

interface ResponsiveTableProps<T = any> {
  columns: ResponsiveTableColumn<T>[];
  data: T[];
  rowKey: string;
  loading?: boolean;
  emptyText?: string;
  actions?: ResponsiveTableAction<T>[];
  onRowClick?: (record: T) => void;
  className?: string;
  mobileBreakpoint?: number; // px
  cardMode?: boolean; // Force card mode
  stickyHeader?: boolean;
  maxHeight?: string;
}

export function ResponsiveTable<T = any>({
  columns,
  data,
  rowKey,
  loading = false,
  emptyText = "Không có dữ liệu",
  actions = [],
  onRowClick,
  className,
  mobileBreakpoint = 768,
  cardMode = false,
  stickyHeader = true,
  maxHeight,
}: ResponsiveTableProps<T>) {
  const [isMobile, setIsMobile] = React.useState(false);
  const [expandedRows, setExpandedRows] = React.useState<Set<string>>(new Set());

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < mobileBreakpoint);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [mobileBreakpoint]);

  const toggleRowExpansion = (key: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedRows(newExpanded);
  };

  const renderCell = (column: ResponsiveTableColumn<T>, record: T, index: number) => {
    if (column.render) {
      return column.render(record, index);
    }
    const value = record[column.key as keyof T];
    return value?.toString() || "";
  };

  const renderMobileCell = (column: ResponsiveTableColumn<T>, record: T, index: number) => {
    if (column.mobileRender) {
      return column.mobileRender(record, index);
    }
    return renderCell(column, record, index);
  };

  // Sort columns by priority for mobile display
  const sortedColumns = React.useMemo(() => {
    return [...columns].sort((a, b) => (a.priority || 3) - (b.priority || 3));
  }, [columns]);

  // Get visible columns for mobile (priority 1-2)
  const mobileVisibleColumns = sortedColumns.filter(col => (col.priority || 3) <= 2);
  const mobileHiddenColumns = sortedColumns.filter(col => (col.priority || 3) > 2);

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }, (_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4" />
                <div className="h-3 bg-gray-200 rounded w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">{emptyText}</p>
        </CardContent>
      </Card>
    );
  }

  // Mobile/Card view
  if (isMobile || cardMode) {
    return (
      <div className={cn("space-y-3", className)}>
        {data.map((record, index) => {
          const key = record[rowKey as keyof T]?.toString() || index.toString();
          const isExpanded = expandedRows.has(key);

          return (
            <Card
              key={key}
              className={cn(
                "transition-all duration-200",
                onRowClick && "cursor-pointer hover:shadow-md"
              )}
              onClick={() => onRowClick?.(record)}
            >
              <CardContent className="p-4">
                {/* Main visible content */}
                <div className="space-y-3">
                  {mobileVisibleColumns.map((column) => (
                    <div key={column.key} className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="text-sm font-medium text-muted-foreground mb-1">
                          {column.title}
                        </div>
                        <div className={cn(
                          "text-sm",
                          column.align === "center" && "text-center",
                          column.align === "right" && "text-right"
                        )}>
                          {renderMobileCell(column, record, index)}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Actions and expand button */}
                  <div className="flex justify-between items-center pt-2 border-t">
                    <div className="flex space-x-2">
                      {actions.length > 0 && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="start">
                            {actions.map((action) => {
                              const Icon = action.icon;
                              return (
                                <DropdownMenuItem
                                  key={action.key}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    action.onClick(record);
                                  }}
                                  disabled={action.disabled?.(record)}
                                  className={cn(
                                    action.variant === "destructive" && "text-destructive focus:text-destructive"
                                  )}
                                >
                                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                                  {action.label}
                                </DropdownMenuItem>
                              );
                            })}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>

                    {mobileHiddenColumns.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleRowExpansion(key);
                        }}
                      >
                        {isExpanded ? (
                          <>
                            <ChevronDown className="h-4 w-4 mr-1" />
                            Thu gọn
                          </>
                        ) : (
                          <>
                            <ChevronRight className="h-4 w-4 mr-1" />
                            Xem thêm
                          </>
                        )}
                      </Button>
                    )}
                  </div>

                  {/* Expanded content */}
                  {isExpanded && mobileHiddenColumns.length > 0 && (
                    <div className="space-y-3 pt-3 border-t">
                      {mobileHiddenColumns.map((column) => (
                        <div key={column.key} className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="text-sm font-medium text-muted-foreground mb-1">
                              {column.title}
                            </div>
                            <div className={cn(
                              "text-sm",
                              column.align === "center" && "text-center",
                              column.align === "right" && "text-right"
                            )}>
                              {renderMobileCell(column, record, index)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  }

  // Desktop table view
  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "overflow-auto rounded-lg border",
          maxHeight && `max-h-[${maxHeight}]`
        )}
      >
        <table className="w-full caption-bottom text-sm">
          <thead
            className={cn(
              "border-b bg-muted/50",
              stickyHeader && "sticky top-0 z-10"
            )}
          >
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "h-12 px-4 text-left align-middle font-medium text-muted-foreground",
                    column.className,
                    column.align === "center" && "text-center",
                    column.align === "right" && "text-right"
                  )}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
              {actions.length > 0 && (
                <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground w-12">
                  Thao tác
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {data.map((record, index) => {
              const key = record[rowKey as keyof T]?.toString() || index.toString();
              return (
                <tr
                  key={key}
                  className={cn(
                    "border-b transition-colors hover:bg-muted/50",
                    onRowClick && "cursor-pointer"
                  )}
                  onClick={() => onRowClick?.(record)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        "p-4 align-middle",
                        column.className,
                        column.align === "center" && "text-center",
                        column.align === "right" && "text-right"
                      )}
                    >
                      {renderCell(column, record, index)}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className="p-4 align-middle text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {actions.map((action) => {
                            const Icon = action.icon;
                            return (
                              <DropdownMenuItem
                                key={action.key}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  action.onClick(record);
                                }}
                                disabled={action.disabled?.(record)}
                                className={cn(
                                  action.variant === "destructive" && "text-destructive focus:text-destructive"
                                )}
                              >
                                {Icon && <Icon className="h-4 w-4 mr-2" />}
                                {action.label}
                              </DropdownMenuItem>
                            );
                          })}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
