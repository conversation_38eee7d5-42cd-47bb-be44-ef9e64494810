"use client";

import { usePathname } from "next/navigation";
import AdminLayoutWithSidebar from "./admin-layout-with-sidebar";
import AdminAuthGuard from "@/components/admin/admin-auth-guard";
import { AdminAuthProvider } from "@/contexts/AdminAuthContext";

export default function AdminLayoutPage({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if current path is auth route
  const isAuthRoute = pathname?.startsWith("/admin/auth");

  // Always wrap with AdminAuthProvider for state management
  if (isAuthRoute) {
    return <AdminAuthProvider>{children}</AdminAuthProvider>;
  }

  // For all other admin routes, wrap with auth provider, guard, and sidebar layout
  return (
    <AdminAuthProvider>
      <AdminAuthGuard>
        <AdminLayoutWithSidebar>{children}</AdminLayoutWithSidebar>
      </AdminAuthGuard>
    </AdminAuthProvider>
  );
}
