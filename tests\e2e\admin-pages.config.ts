import { defineConfig, devices } from '@playwright/test';

/**
 * Configuration specifically for Admin Pages E2E tests
 * This can be used to run only the admin pages tests with specific settings
 */
export default defineConfig({
  testDir: './tests/e2e',
  testMatch: ['**/admin-pages*.spec.ts'],
  
  /* Run tests in files in parallel */
  fullyParallel: false, // Disable for admin tests to avoid conflicts
  
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 1,
  
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : 2, // Limited workers for admin tests
  
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'test-results/admin-pages-html' }],
    ['json', { outputFile: 'test-results/admin-pages-results.json' }],
    ['junit', { outputFile: 'test-results/admin-pages-results.xml' }]
  ],
  
  /* Shared settings for all the projects below. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',

    /* Collect trace when retrying the failed test. */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    
    /* Record video on failure */
    video: 'retain-on-failure',
    
    /* Global timeout for each action */
    actionTimeout: 15000, // Increased for admin operations
    
    /* Global timeout for navigation */
    navigationTimeout: 30000,
    
    /* Ignore HTTPS errors */
    ignoreHTTPSErrors: true,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium-admin-pages',
      use: { 
        ...devices['Desktop Chrome'],
        // Admin-specific settings
        viewport: { width: 1920, height: 1080 }, // Larger viewport for admin interface
      },
    },

    {
      name: 'firefox-admin-pages',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 },
      },
    },

    {
      name: 'webkit-admin-pages',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 },
      },
    },

    /* Test against mobile viewports for responsive admin interface */
    {
      name: 'mobile-chrome-admin',
      use: { 
        ...devices['Pixel 5'],
        // Mobile admin testing
      },
    },
    {
      name: 'mobile-safari-admin',
      use: { 
        ...devices['iPhone 12'],
      },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./global-setup.ts'),
  globalTeardown: require.resolve('./global-teardown.ts'),

  /* Test timeout - increased for admin operations */
  timeout: 60 * 1000,
  expect: {
    /* Timeout for expect() assertions */
    timeout: 10000, // Increased for admin operations
  },
});
