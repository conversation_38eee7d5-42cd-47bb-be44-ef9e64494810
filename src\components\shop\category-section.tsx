'use client';

import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const categories = [
	{
		id: 1,
		name: '<PERSON><PERSON> thun',
		slug: 'ao-thun',
		image: '/images/categories/t-shirts.jpg',
		productCount: 120,
		color: 'from-blue-400 to-blue-600',
	},
	{
		id: 2,
		name: '<PERSON><PERSON><PERSON> đ<PERSON>',
		slug: 'vay-dam',
		image: '/images/categories/dresses.jpg',
		productCount: 85,
		color: 'from-pink-400 to-pink-600',
	},
	{
		id: 3,
		name: 'Quần jeans',
		slug: 'quan-jeans',
		image: '/images/categories/jeans.jpg',
		productCount: 95,
		color: 'from-indigo-400 to-indigo-600',
	},
	{
		id: 4,
		name: '<PERSON><PERSON> kho<PERSON><PERSON>',
		slug: 'ao-khoac',
		image: '/images/categories/jackets.jpg',
		productCount: 67,
		color: 'from-purple-400 to-purple-600',
	},
	{
		id: 5,
		name: '<PERSON><PERSON> kiện',
		slug: 'phu-kien',
		image: '/images/categories/accessories.jpg',
		productCount: 150,
		color: 'from-green-400 to-green-600',
	},
	{
		id: 6,
		name: 'Giày dép',
		slug: 'giay-dep',
		image: '/images/categories/shoes.jpg',
		productCount: 78,
		color: 'from-orange-400 to-orange-600',
	},
];

export function CategorySection() {
	return (
		<section className="py-16 lg:py-24">
			<div className="container mx-auto px-4">
				{/* Header */}
				<div className="text-center mb-12">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4">
						Danh mục sản phẩm
					</h2>
					<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
						Khám phá bộ sưu tập đa dạng của chúng tôi với nhiều danh mục thời trang khác nhau
					</p>
				</div>

				{/* Categories Grid */}
				<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6">
					{categories.map((category) => (
						<Link key={category.id} href={`/categories/${category.slug}`}>
							<Card className="group cursor-pointer border-0 bg-transparent">
								<CardContent className="p-0">
									<div className="relative overflow-hidden rounded-xl">
										{/* Background with gradient */}
										<div className={`aspect-square bg-gradient-to-br ${category.color} p-6 flex flex-col justify-between transition-transform duration-300 group-hover:scale-105`}>
											{/* Category Icon/Image placeholder */}
											<div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
												<div className="w-6 h-6 bg-white/40 rounded" />
											</div>
											
											{/* Category Info */}
											<div className="text-white">
												<h3 className="font-semibold text-sm lg:text-base mb-1">
													{category.name}
												</h3>
												<p className="text-xs text-white/80">
													{category.productCount} sản phẩm
												</p>
											</div>

											{/* Hover Arrow */}
											<div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
												<ArrowRight className="h-4 w-4 text-white" />
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						</Link>
					))}
				</div>

				{/* View All Button */}
				<div className="text-center mt-12">
					<Link 
						href="/categories"
						className="inline-flex items-center space-x-2 text-fashion-600 hover:text-fashion-700 font-medium transition-colors"
					>
						<span>Xem tất cả danh mục</span>
						<ArrowRight className="h-4 w-4" />
					</Link>
				</div>
			</div>
		</section>
	);
}
