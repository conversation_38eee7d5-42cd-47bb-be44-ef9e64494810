// Email template types and interfaces
export interface EmailTemplateData {
  recipientName: string;
  recipientEmail: string;
  [key: string]: any;
}

export interface WelcomeEmailData extends EmailTemplateData {
  loginUrl: string;
  supportEmail: string;
}

export interface OrderConfirmationEmailData extends EmailTemplateData {
  order: {
    id: string;
    total: number;
    status: string;
    createdAt: string;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
      image?: string;
    }>;
    shippingAddress: {
      fullName: string;
      address: string;
      city: string;
      postalCode: string;
      phone: string;
    };
  };
  trackingUrl?: string;
}

export interface ContactFormEmailData extends EmailTemplateData {
  senderName: string;
  senderEmail: string;
  senderPhone?: string;
  subject: string;
  message: string;
  submittedAt: string;
}

export interface PasswordResetEmailData extends EmailTemplateData {
  resetUrl: string;
  expiresAt: string;
}

export interface AdminNotificationEmailData extends EmailTemplateData {
  notification: {
    id: string;
    title: string;
    message: string;
    type: string;
    priority: string;
    actionUrl?: string;
    createdAt: string;
  };
  sender?: {
    name: string;
    email: string;
  };
}

// Base email template with NS Shop branding
export const getBaseTemplate = (content: string, title: string) => `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .content h2 {
            color: #1f2937;
            font-size: 24px;
            margin-bottom: 20px;
        }
        
        .content p {
            margin-bottom: 16px;
            color: #4b5563;
            font-size: 16px;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
            color: white;
            padding: 14px 28px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .info-box {
            background-color: #f3f4f6;
            border-left: 4px solid #ec4899;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .footer {
            background-color: #1f2937;
            color: #9ca3af;
            padding: 30px 20px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer a {
            color: #ec4899;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #ec4899;
            text-decoration: none;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NS Shop</h1>
            <p>Thời trang trực tuyến chất lượng cao</p>
        </div>
        
        <div class="content">
            ${content}
        </div>
        
        <div class="footer">
            <div class="social-links">
                <a href="#">Facebook</a>
                <a href="#">Instagram</a>
                <a href="#">Twitter</a>
            </div>
            <p>
                © 2024 NS Shop. Tất cả quyền được bảo lưu.<br>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}">Truy cập website</a> |
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/contact">Liên hệ hỗ trợ</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px; color: #6b7280;">
                Bạn nhận được email này vì bạn đã đăng ký tài khoản hoặc sử dụng dịch vụ của NS Shop.
            </p>
        </div>
    </div>
</body>
</html>
`;

// Welcome email template
export const generateWelcomeEmail = (data: WelcomeEmailData) => {
  const content = `
    <h2>Chào mừng ${data.recipientName} đến với NS Shop! 🎉</h2>

    <p>Cảm ơn bạn đã đăng ký tài khoản tại NS Shop. Chúng tôi rất vui mừng được chào đón bạn vào cộng đồng thời trang của chúng tôi!</p>

    <div class="info-box">
        <h3 style="color: #1f2937; margin-bottom: 10px;">Tài khoản của bạn đã sẵn sàng</h3>
        <p style="margin-bottom: 8px;"><strong>Email:</strong> ${data.recipientEmail}</p>
        <p style="margin-bottom: 0;"><strong>Tên:</strong> ${data.recipientName}</p>
    </div>

    <p>Bây giờ bạn có thể:</p>
    <ul style="margin-left: 20px; margin-bottom: 20px; color: #4b5563;">
        <li>Duyệt qua hàng nghìn sản phẩm thời trang chất lượng cao</li>
        <li>Tạo danh sách yêu thích của riêng bạn</li>
        <li>Theo dõi đơn hàng và lịch sử mua sắm</li>
        <li>Nhận thông báo về các ưu đãi đặc biệt</li>
    </ul>

    <div style="text-align: center;">
        <a href="${data.loginUrl}" class="button">Bắt đầu mua sắm ngay</a>
    </div>

    <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi tại <a href="mailto:${data.supportEmail}" style="color: #ec4899;">${data.supportEmail}</a>.</p>

    <p>Chúc bạn có những trải nghiệm mua sắm tuyệt vời!</p>
  `;

  return {
    subject: `Chào mừng ${data.recipientName} đến với NS Shop! 🎉`,
    html: getBaseTemplate(content, "Chào mừng đến với NS Shop"),
    text: `Chào mừng ${data.recipientName} đến với NS Shop!\n\nCảm ơn bạn đã đăng ký tài khoản. Tài khoản của bạn đã sẵn sàng sử dụng.\n\nĐăng nhập tại: ${data.loginUrl}\n\nLiên hệ hỗ trợ: ${data.supportEmail}`,
  };
};

// Order confirmation email template
export const generateOrderConfirmationEmail = (
  data: OrderConfirmationEmailData
) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const orderItemsHtml = data.order.items
    .map(
      (item) => `
    <tr>
      <td style="padding: 15px; border-bottom: 1px solid #e5e7eb;">
        <div style="display: flex; align-items: center;">
          ${item.image ? `<img src="${item.image}" alt="${item.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin-right: 15px;">` : ""}
          <div>
            <h4 style="margin: 0; color: #1f2937; font-size: 16px;">${item.name}</h4>
            <p style="margin: 5px 0 0 0; color: #6b7280; font-size: 14px;">Số lượng: ${item.quantity}</p>
          </div>
        </div>
      </td>
      <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; text-align: right; font-weight: 600; color: #1f2937;">
        ${formatCurrency(item.price * item.quantity)}
      </td>
    </tr>
  `
    )
    .join("");

  const content = `
    <h2>Xác nhận đơn hàng #${data.order.id.slice(-8).toUpperCase()}</h2>

    <p>Xin chào ${data.recipientName},</p>

    <p>Cảm ơn bạn đã đặt hàng tại NS Shop! Chúng tôi đã nhận được đơn hàng của bạn và đang xử lý.</p>

    <div class="info-box">
        <h3 style="color: #1f2937; margin-bottom: 15px;">Thông tin đơn hàng</h3>
        <p style="margin-bottom: 8px;"><strong>Mã đơn hàng:</strong> #${data.order.id.slice(-8).toUpperCase()}</p>
        <p style="margin-bottom: 8px;"><strong>Ngày đặt:</strong> ${new Date(data.order.createdAt).toLocaleDateString("vi-VN")}</p>
        <p style="margin-bottom: 8px;"><strong>Trạng thái:</strong> ${data.order.status}</p>
        <p style="margin-bottom: 0;"><strong>Tổng tiền:</strong> ${formatCurrency(data.order.total)}</p>
    </div>

    <h3 style="color: #1f2937; margin-top: 30px; margin-bottom: 15px;">Chi tiết sản phẩm</h3>
    <table style="width: 100%; border-collapse: collapse; background-color: #f9fafb; border-radius: 8px; overflow: hidden;">
      ${orderItemsHtml}
      <tr style="background-color: #f3f4f6;">
        <td style="padding: 20px; font-weight: 600; color: #1f2937; font-size: 18px;">
          Tổng cộng
        </td>
        <td style="padding: 20px; text-align: right; font-weight: 700; color: #ec4899; font-size: 18px;">
          ${formatCurrency(data.order.total)}
        </td>
      </tr>
    </table>

    <h3 style="color: #1f2937; margin-top: 30px; margin-bottom: 15px;">Địa chỉ giao hàng</h3>
    <div class="info-box">
        <p style="margin-bottom: 8px;"><strong>Người nhận:</strong> ${data.order.shippingAddress.fullName}</p>
        <p style="margin-bottom: 8px;"><strong>Địa chỉ:</strong> ${data.order.shippingAddress.address}</p>
        <p style="margin-bottom: 8px;"><strong>Thành phố:</strong> ${data.order.shippingAddress.city}</p>
        <p style="margin-bottom: 8px;"><strong>Mã bưu điện:</strong> ${data.order.shippingAddress.postalCode}</p>
        <p style="margin-bottom: 0;"><strong>Số điện thoại:</strong> ${data.order.shippingAddress.phone}</p>
    </div>

    ${
      data.trackingUrl
        ? `
    <div style="text-align: center; margin: 30px 0;">
        <a href="${data.trackingUrl}" class="button">Theo dõi đơn hàng</a>
    </div>
    `
        : ""
    }

    <p>Chúng tôi sẽ gửi email thông báo khi đơn hàng được giao cho đơn vị vận chuyển.</p>

    <p>Cảm ơn bạn đã tin tưởng và mua sắm tại NS Shop!</p>
  `;

  return {
    subject: `Xác nhận đơn hàng #${data.order.id.slice(-8).toUpperCase()} - NS Shop`,
    html: getBaseTemplate(content, "Xác nhận đơn hàng"),
    text: `Xác nhận đơn hàng #${data.order.id.slice(-8).toUpperCase()}\n\nXin chào ${data.recipientName},\n\nCảm ơn bạn đã đặt hàng tại NS Shop!\n\nMã đơn hàng: #${data.order.id.slice(-8).toUpperCase()}\nTổng tiền: ${formatCurrency(data.order.total)}\nTrạng thái: ${data.order.status}\n\n${data.trackingUrl ? `Theo dõi đơn hàng: ${data.trackingUrl}\n\n` : ""}Cảm ơn bạn đã tin tưởng NS Shop!`,
  };
};

// Contact form email template
export const generateContactFormEmail = (data: ContactFormEmailData) => {
  const content = `
    <h2>Tin nhắn liên hệ mới từ website</h2>

    <p>Bạn đã nhận được một tin nhắn liên hệ mới từ khách hàng qua website NS Shop.</p>

    <div class="info-box">
        <h3 style="color: #1f2937; margin-bottom: 15px;">Thông tin người gửi</h3>
        <p style="margin-bottom: 8px;"><strong>Tên:</strong> ${data.senderName}</p>
        <p style="margin-bottom: 8px;"><strong>Email:</strong> ${data.senderEmail}</p>
        ${data.senderPhone ? `<p style="margin-bottom: 8px;"><strong>Số điện thoại:</strong> ${data.senderPhone}</p>` : ""}
        <p style="margin-bottom: 8px;"><strong>Chủ đề:</strong> ${data.subject}</p>
        <p style="margin-bottom: 0;"><strong>Thời gian gửi:</strong> ${new Date(data.submittedAt).toLocaleString("vi-VN")}</p>
    </div>

    <h3 style="color: #1f2937; margin-top: 30px; margin-bottom: 15px;">Nội dung tin nhắn</h3>
    <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; border-left: 4px solid #ec4899;">
        <p style="margin: 0; white-space: pre-wrap; color: #374151; line-height: 1.6;">${data.message}</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="mailto:${data.senderEmail}?subject=Re: ${encodeURIComponent(data.subject)}" class="button">Trả lời email</a>
    </div>

    <p style="font-size: 14px; color: #6b7280;">
        <strong>Lưu ý:</strong> Hãy trả lời khách hàng trong thời gian sớm nhất để tạo ấn tượng tốt về dịch vụ khách hàng của NS Shop.
    </p>
  `;

  return {
    subject: `[Liên hệ] ${data.subject} - từ ${data.senderName}`,
    html: getBaseTemplate(content, "Tin nhắn liên hệ mới"),
    text: `Tin nhắn liên hệ mới từ ${data.senderName}\n\nEmail: ${data.senderEmail}\n${data.senderPhone ? `Điện thoại: ${data.senderPhone}\n` : ""}Chủ đề: ${data.subject}\n\nNội dung:\n${data.message}\n\nThời gian: ${new Date(data.submittedAt).toLocaleString("vi-VN")}`,
  };
};

// Password reset email template
export const generatePasswordResetEmail = (data: PasswordResetEmailData) => {
  const content = `
    <h2>Yêu cầu đặt lại mật khẩu</h2>

    <p>Xin chào ${data.recipientName},</p>

    <p>Chúng tôi đã nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn tại NS Shop.</p>

    <div class="info-box">
        <h3 style="color: #1f2937; margin-bottom: 10px;">⚠️ Thông tin quan trọng</h3>
        <p style="margin-bottom: 8px;">• Link đặt lại mật khẩu chỉ có hiệu lực trong <strong>15 phút</strong></p>
        <p style="margin-bottom: 8px;">• Hết hạn lúc: <strong>${new Date(data.expiresAt).toLocaleString("vi-VN")}</strong></p>
        <p style="margin-bottom: 0;">• Nếu bạn không yêu cầu đặt lại mật khẩu, hãy bỏ qua email này</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="${data.resetUrl}" class="button">Đặt lại mật khẩu</a>
    </div>

    <p>Hoặc copy và dán link sau vào trình duyệt:</p>
    <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">
        ${data.resetUrl}
    </p>

    <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0; color: #dc2626; font-size: 14px;">
            <strong>Bảo mật:</strong> Nếu bạn không yêu cầu đặt lại mật khẩu, có thể ai đó đang cố gắng truy cập tài khoản của bạn.
            Hãy liên hệ với chúng tôi ngay lập tức.
        </p>
    </div>
  `;

  return {
    subject: "Đặt lại mật khẩu tài khoản NS Shop",
    html: getBaseTemplate(content, "Đặt lại mật khẩu"),
    text: `Đặt lại mật khẩu tài khoản NS Shop\n\nXin chào ${data.recipientName},\n\nChúng tôi đã nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.\n\nClick vào link sau để đặt lại mật khẩu:\n${data.resetUrl}\n\nLink có hiệu lực đến: ${new Date(data.expiresAt).toLocaleString("vi-VN")}\n\nNếu bạn không yêu cầu đặt lại mật khẩu, hãy bỏ qua email này.`,
  };
};

// Admin notification email template
export const generateAdminNotificationEmail = (
  data: AdminNotificationEmailData
) => {
  const typeColors = {
    INFO: "#3b82f6",
    SUCCESS: "#10b981",
    WARNING: "#f59e0b",
    ERROR: "#ef4444",
    SYSTEM: "#6366f1",
  };

  const priorityLabels = {
    LOW: "Thấp",
    NORMAL: "Bình thường",
    HIGH: "Cao",
    URGENT: "Khẩn cấp",
  };

  const typeLabels = {
    INFO: "Thông tin",
    SUCCESS: "Thành công",
    WARNING: "Cảnh báo",
    ERROR: "Lỗi",
    SYSTEM: "Hệ thống",
  };

  const typeColor =
    typeColors[data.notification.type as keyof typeof typeColors] || "#6b7280";
  const typeLabel =
    typeLabels[data.notification.type as keyof typeof typeLabels] ||
    data.notification.type;
  const priorityLabel =
    priorityLabels[data.notification.priority as keyof typeof priorityLabels] ||
    data.notification.priority;

  const content = `
    <h2>Thông báo Admin - ${data.notification.title}</h2>

    <p>Xin chào ${data.recipientName},</p>

    <p>Bạn có một thông báo mới từ hệ thống NS Shop Admin.</p>

    <div class="info-box">
        <h3 style="color: #1f2937; margin-bottom: 15px;">Chi tiết thông báo</h3>
        <p style="margin-bottom: 8px;"><strong>Tiêu đề:</strong> ${data.notification.title}</p>
        <p style="margin-bottom: 8px;">
            <strong>Loại:</strong>
            <span style="background-color: ${typeColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                ${typeLabel}
            </span>
        </p>
        <p style="margin-bottom: 8px;"><strong>Độ ưu tiên:</strong> ${priorityLabel}</p>
        <p style="margin-bottom: 8px;"><strong>Thời gian:</strong> ${new Date(data.notification.createdAt).toLocaleString("vi-VN")}</p>
        ${data.sender ? `<p style="margin-bottom: 0;"><strong>Người gửi:</strong> ${data.sender.name} (${data.sender.email})</p>` : ""}
    </div>

    <h3 style="color: #1f2937; margin-top: 30px; margin-bottom: 15px;">Nội dung</h3>
    <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; border-left: 4px solid ${typeColor};">
        <p style="margin: 0; white-space: pre-wrap; color: #374151; line-height: 1.6;">${data.notification.message}</p>
    </div>

    ${
      data.notification.actionUrl
        ? `
    <div style="text-align: center; margin: 30px 0;">
        <a href="${data.notification.actionUrl}" class="button">Xem chi tiết</a>
    </div>
    `
        : ""
    }

    <p style="font-size: 14px; color: #6b7280;">
        Bạn nhận được email này vì bạn là admin của NS Shop.
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/notifications" style="color: #ec4899;">Quản lý thông báo</a>
    </p>
  `;

  return {
    subject: `[NS Shop Admin] ${data.notification.title}`,
    html: getBaseTemplate(content, "Thông báo Admin"),
    text: `NS Shop Admin - Thông báo mới\n\n${data.notification.title}\n\n${data.notification.message}\n\nLoại: ${typeLabel}\nĐộ ưu tiên: ${priorityLabel}\nThời gian: ${new Date(data.notification.createdAt).toLocaleString("vi-VN")}\n${data.sender ? `Người gửi: ${data.sender.name}` : ""}\n\n${data.notification.actionUrl ? `Xem chi tiết: ${data.notification.actionUrl}` : ""}`,
  };
};
