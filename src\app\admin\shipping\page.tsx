"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Truck,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  MapPin,
  Clock,
  Package,
  DollarSign,
  Settings,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";

interface ShippingZone {
  id: string;
  name: string;
  description?: string;
  provinces: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    methods: number;
  };
}

interface ShippingMethod {
  id: string;
  name: string;
  description?: string;
  type: "STANDARD" | "EXPRESS" | "SAME_DAY" | "PICKUP" | "CUSTOM";
  baseFee: number;
  freeShippingMin?: number;
  estimatedDays: string;
  maxWeight?: number;
  maxDimensions?: any;
  isActive: boolean;
  sortOrder: number;
  zoneId: string;
  createdAt: string;
  updatedAt: string;
  zone: {
    id: string;
    name: string;
  };
}

const shippingMethodTypeLabels = {
  STANDARD: "Tiêu chuẩn",
  EXPRESS: "Nhanh",
  SAME_DAY: "Trong ngày",
  PICKUP: "Nhận tại cửa hàng",
  CUSTOM: "Tùy chỉnh",
};

const shippingMethodTypeColors = {
  STANDARD: "bg-blue-100 text-blue-800",
  EXPRESS: "bg-green-100 text-green-800",
  SAME_DAY: "bg-purple-100 text-purple-800",
  PICKUP: "bg-orange-100 text-orange-800",
  CUSTOM: "bg-gray-100 text-gray-800",
};

// Vietnam provinces list
const vietnamProvinces = [
  "Hà Nội",
  "TP. Hồ Chí Minh",
  "Hải Phòng",
  "Đà Nẵng",
  "Cần Thơ",
  "An Giang",
  "Bà Rịa - Vũng Tàu",
  "Bắc Giang",
  "Bắc Kạn",
  "Bạc Liêu",
  "Bắc Ninh",
  "Bến Tre",
  "Bình Định",
  "Bình Dương",
  "Bình Phước",
  "Bình Thuận",
  "Cà Mau",
  "Cao Bằng",
  "Đắk Lắk",
  "Đắk Nông",
  "Điện Biên",
  "Đồng Nai",
  "Đồng Tháp",
  "Gia Lai",
  "Hà Giang",
  "Hà Nam",
  "Hà Tĩnh",
  "Hải Dương",
  "Hậu Giang",
  "Hòa Bình",
  "Hưng Yên",
  "Khánh Hòa",
  "Kiên Giang",
  "Kon Tum",
  "Lai Châu",
  "Lâm Đồng",
  "Lạng Sơn",
  "Lào Cai",
  "Long An",
  "Nam Định",
  "Nghệ An",
  "Ninh Bình",
  "Ninh Thuận",
  "Phú Thọ",
  "Phú Yên",
  "Quảng Bình",
  "Quảng Nam",
  "Quảng Ngãi",
  "Quảng Ninh",
  "Quảng Trị",
  "Sóc Trăng",
  "Sơn La",
  "Tây Ninh",
  "Thái Bình",
  "Thái Nguyên",
  "Thanh Hóa",
  "Thừa Thiên Huế",
  "Tiền Giang",
  "Trà Vinh",
  "Tuyên Quang",
  "Vĩnh Long",
  "Vĩnh Phúc",
  "Yên Bái",
];

export default function AdminShippingPage() {
  const [activeTab, setActiveTab] = useState("zones");
  const [selectedZone, setSelectedZone] = useState<ShippingZone | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<ShippingMethod | null>(
    null
  );
  const [isCreateZoneDialogOpen, setIsCreateZoneDialogOpen] = useState(false);
  const [isEditZoneDialogOpen, setIsEditZoneDialogOpen] = useState(false);
  const [isCreateMethodDialogOpen, setIsCreateMethodDialogOpen] =
    useState(false);
  const [isEditMethodDialogOpen, setIsEditMethodDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Form state for zones
  const [zoneFormData, setZoneFormData] = useState({
    name: "",
    description: "",
    provinces: [] as string[],
    isActive: true,
  });

  // Form state for methods
  const [methodFormData, setMethodFormData] = useState<{
    name: string;
    description: string;
    type: ShippingMethod["type"];
    baseFee: number;
    freeShippingMin: string;
    estimatedDays: string;
    maxWeight: string;
    isActive: boolean;
    zoneId: string;
  }>({
    name: "",
    description: "",
    type: "STANDARD",
    baseFee: 0,
    freeShippingMin: "",
    estimatedDays: "",
    maxWeight: "",
    isActive: true,
    zoneId: "",
  });

  // Data fetching for zones
  const {
    data: zones,
    loading: zonesLoading,
    pagination: zonesPagination,
    refresh: refreshZones,
    setParams: setZonesParams,
  } = useAdminData<ShippingZone>({
    endpoint: "/api/admin/shipping/zones",
    initialParams: { page: 1, limit: 20 },
  });

  // Data fetching for methods
  const {
    data: methods,
    loading: methodsLoading,
    pagination: methodsPagination,
    refresh: refreshMethods,
    setParams: setMethodsParams,
  } = useAdminData<ShippingMethod>({
    endpoint: "/api/admin/shipping/methods",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createZone,
    update: updateZone,
    remove: deleteZone,
  } = useAdminCrud("/api/admin/shipping/zones");
  const {
    create: createMethod,
    update: updateMethod,
    remove: deleteMethod,
  } = useAdminCrud("/api/admin/shipping/methods");

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    if (activeTab === "zones") {
      setZonesParams({ search: value, page: 1 });
    } else {
      setMethodsParams({ search: value, page: 1 });
    }
  };

  // Zone handlers
  const handleViewZone = (zone: ShippingZone) => {
    setSelectedZone(zone);
  };

  const handleEditZone = (zone: ShippingZone) => {
    setZoneFormData({
      name: zone.name,
      description: zone.description || "",
      provinces: zone.provinces,
      isActive: zone.isActive,
    });
    setSelectedZone(zone);
    setIsEditZoneDialogOpen(true);
  };

  const handleDeleteZone = async (zone: ShippingZone) => {
    if (confirm("Bạn có chắc chắn muốn xóa khu vực giao hàng này?")) {
      const result = await deleteZone(zone.id);
      if (result) {
        toast.success("Đã xóa khu vực giao hàng");
        refreshZones();
      }
    }
  };

  // Method handlers
  const handleViewMethod = (method: ShippingMethod) => {
    setSelectedMethod(method);
  };

  const handleEditMethod = (method: ShippingMethod) => {
    setMethodFormData({
      name: method.name,
      description: method.description || "",
      type: method.type,
      baseFee: method.baseFee,
      freeShippingMin: method.freeShippingMin?.toString() || "",
      estimatedDays: method.estimatedDays,
      maxWeight: method.maxWeight?.toString() || "",
      isActive: method.isActive,
      zoneId: method.zoneId,
    });
    setSelectedMethod(method);
    setIsEditMethodDialogOpen(true);
  };

  const handleDeleteMethod = async (method: ShippingMethod) => {
    if (confirm("Bạn có chắc chắn muốn xóa phương thức giao hàng này?")) {
      const result = await deleteMethod(method.id);
      if (result) {
        toast.success("Đã xóa phương thức giao hàng");
        refreshMethods();
      }
    }
  };

  // Form submit handlers
  const handleZoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let result;
    if (selectedZone && isEditZoneDialogOpen) {
      result = await updateZone(selectedZone.id, zoneFormData);
    } else {
      result = await createZone(zoneFormData);
    }

    if (result) {
      toast.success(
        selectedZone
          ? "Đã cập nhật khu vực giao hàng"
          : "Đã tạo khu vực giao hàng mới"
      );
      setIsCreateZoneDialogOpen(false);
      setIsEditZoneDialogOpen(false);
      setSelectedZone(null);
      resetZoneForm();
      refreshZones();
    }
  };

  const handleMethodSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const data = {
      ...methodFormData,
      baseFee: Number(methodFormData.baseFee),
      freeShippingMin: methodFormData.freeShippingMin
        ? Number(methodFormData.freeShippingMin)
        : undefined,
      maxWeight: methodFormData.maxWeight
        ? Number(methodFormData.maxWeight)
        : undefined,
    };

    let result;
    if (selectedMethod && isEditMethodDialogOpen) {
      result = await updateMethod(selectedMethod.id, data);
    } else {
      result = await createMethod(data);
    }

    if (result) {
      toast.success(
        selectedMethod
          ? "Đã cập nhật phương thức giao hàng"
          : "Đã tạo phương thức giao hàng mới"
      );
      setIsCreateMethodDialogOpen(false);
      setIsEditMethodDialogOpen(false);
      setSelectedMethod(null);
      resetMethodForm();
      refreshMethods();
    }
  };

  // Reset forms
  const resetZoneForm = () => {
    setZoneFormData({
      name: "",
      description: "",
      provinces: [],
      isActive: true,
    });
  };

  const resetMethodForm = () => {
    setMethodFormData({
      name: "",
      description: "",
      type: "STANDARD",
      baseFee: 0,
      freeShippingMin: "",
      estimatedDays: "",
      maxWeight: "",
      isActive: true,
      zoneId: "",
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Truck className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Vận chuyển</h1>
            <p className="text-muted-foreground">
              Cấu hình khu vực và phương thức giao hàng
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Khu vực giao hàng
                </p>
                <p className="text-2xl font-bold">{zones?.length || 0}</p>
              </div>
              <MapPin className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Phương thức vận chuyển
                </p>
                <p className="text-2xl font-bold">{methods?.length || 0}</p>
              </div>
              <Truck className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Khu vực hoạt động
                </p>
                <p className="text-2xl font-bold">
                  {zones?.filter((z) => z.isActive).length || 0}
                </p>
              </div>
              <Settings className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Phương thức hoạt động
                </p>
                <p className="text-2xl font-bold">
                  {methods?.filter((m) => m.isActive).length || 0}
                </p>
              </div>
              <Package className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Tìm kiếm khu vực hoặc phương thức giao hàng..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="zones">Khu vực giao hàng</TabsTrigger>
          <TabsTrigger value="methods">Phương thức vận chuyển</TabsTrigger>
        </TabsList>

        {/* Zones Tab */}
        <TabsContent value="zones" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Khu vực giao hàng</h2>
            <Dialog
              open={isCreateZoneDialogOpen}
              onOpenChange={setIsCreateZoneDialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={resetZoneForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm khu vực
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={zones || []}
                config={{
                  columns: [
                    {
                      key: "name",
                      title: "Tên khu vực",
                      sortable: true,
                      render: (zone: ShippingZone) => (
                        <div className="space-y-1">
                          <div className="font-medium">{zone.name}</div>
                          {zone.description && (
                            <div className="text-sm text-muted-foreground">
                              {zone.description}
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "provinces",
                      title: "Tỉnh/Thành phố",
                      render: (zone: ShippingZone) => (
                        <div className="space-y-1">
                          <div className="text-sm">
                            {zone.provinces.length} tỉnh/thành phố
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {zone.provinces.slice(0, 3).join(", ")}
                            {zone.provinces.length > 3 && "..."}
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "methods",
                      title: "Phương thức",
                      render: (zone: ShippingZone) => (
                        <div className="text-sm">
                          {zone._count.methods} phương thức
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (zone: ShippingZone) => (
                        <Badge
                          variant="secondary"
                          className={
                            zone.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {zone.isActive ? "Hoạt động" : "Tạm dừng"}
                        </Badge>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: handleViewZone,
                      },
                      {
                        key: "edit",
                        label: "Chỉnh sửa",
                        icon: Edit,
                        onClick: handleEditZone,
                      },
                      {
                        key: "delete",
                        label: "Xóa",
                        icon: Trash2,
                        onClick: handleDeleteZone,
                        type: "danger" as const,
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={zonesLoading}
                pagination={
                  zonesPagination
                    ? {
                        current: zonesPagination.page,
                        pageSize: zonesPagination.limit,
                        total: zonesPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setZonesParams({ page })}
                onPageSizeChange={(limit) => setZonesParams({ limit, page: 1 })}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Methods Tab */}
        <TabsContent value="methods" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Phương thức vận chuyển</h2>
            <Dialog
              open={isCreateMethodDialogOpen}
              onOpenChange={setIsCreateMethodDialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={resetMethodForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm phương thức
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={methods || []}
                config={{
                  columns: [
                    {
                      key: "method",
                      title: "Phương thức",
                      render: (method: ShippingMethod) => (
                        <div className="space-y-1">
                          <div className="font-medium">{method.name}</div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="secondary"
                              className={shippingMethodTypeColors[method.type]}
                            >
                              {shippingMethodTypeLabels[method.type]}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {method.estimatedDays}
                            </span>
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "zone",
                      title: "Khu vực",
                      render: (method: ShippingMethod) => (
                        <div className="text-sm">{method.zone.name}</div>
                      ),
                    },
                    {
                      key: "fee",
                      title: "Phí giao hàng",
                      render: (method: ShippingMethod) => (
                        <div className="space-y-1">
                          <div className="font-medium">
                            {formatCurrency(method.baseFee)}
                          </div>
                          {method.freeShippingMin && (
                            <div className="text-xs text-muted-foreground">
                              Miễn phí từ{" "}
                              {formatCurrency(method.freeShippingMin)}
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (method: ShippingMethod) => (
                        <Badge
                          variant="secondary"
                          className={
                            method.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {method.isActive ? "Hoạt động" : "Tạm dừng"}
                        </Badge>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: handleViewMethod,
                      },
                      {
                        key: "edit",
                        label: "Chỉnh sửa",
                        icon: Edit,
                        onClick: handleEditMethod,
                      },
                      {
                        key: "delete",
                        label: "Xóa",
                        icon: Trash2,
                        onClick: handleDeleteMethod,
                        type: "danger" as const,
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={methodsLoading}
                pagination={
                  methodsPagination
                    ? {
                        current: methodsPagination.page,
                        pageSize: methodsPagination.limit,
                        total: methodsPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setMethodsParams({ page })}
                onPageSizeChange={(limit) =>
                  setMethodsParams({ limit, page: 1 })
                }
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Zone Dialog */}
      <Dialog
        open={isCreateZoneDialogOpen || isEditZoneDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateZoneDialogOpen(false);
            setIsEditZoneDialogOpen(false);
            setSelectedZone(null);
            resetZoneForm();
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedZone
                ? "Chỉnh sửa khu vực giao hàng"
                : "Tạo khu vực giao hàng mới"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleZoneSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="zoneName">Tên khu vực *</Label>
              <Input
                id="zoneName"
                value={zoneFormData.name}
                onChange={(e) =>
                  setZoneFormData({ ...zoneFormData, name: e.target.value })
                }
                placeholder="Nhập tên khu vực"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="zoneDescription">Mô tả</Label>
              <Textarea
                id="zoneDescription"
                value={zoneFormData.description}
                onChange={(e) =>
                  setZoneFormData({
                    ...zoneFormData,
                    description: e.target.value,
                  })
                }
                placeholder="Nhập mô tả khu vực"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Tỉnh/Thành phố *</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto border rounded-lg p-3">
                {vietnamProvinces.map((province) => (
                  <div key={province} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`province-${province}`}
                      checked={zoneFormData.provinces.includes(province)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setZoneFormData({
                            ...zoneFormData,
                            provinces: [...zoneFormData.provinces, province],
                          });
                        } else {
                          setZoneFormData({
                            ...zoneFormData,
                            provinces: zoneFormData.provinces.filter(
                              (p) => p !== province
                            ),
                          });
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={`province-${province}`} className="text-sm">
                      {province}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Đã chọn: {zoneFormData.provinces.length} tỉnh/thành phố
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="zoneIsActive"
                checked={zoneFormData.isActive}
                onCheckedChange={(checked) =>
                  setZoneFormData({ ...zoneFormData, isActive: checked })
                }
              />
              <Label htmlFor="zoneIsActive">Kích hoạt khu vực</Label>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateZoneDialogOpen(false);
                  setIsEditZoneDialogOpen(false);
                  setSelectedZone(null);
                  resetZoneForm();
                }}
              >
                Hủy
              </Button>
              <Button type="submit">
                {selectedZone ? "Cập nhật" : "Tạo mới"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create/Edit Method Dialog */}
      <Dialog
        open={isCreateMethodDialogOpen || isEditMethodDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateMethodDialogOpen(false);
            setIsEditMethodDialogOpen(false);
            setSelectedMethod(null);
            resetMethodForm();
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedMethod
                ? "Chỉnh sửa phương thức vận chuyển"
                : "Tạo phương thức vận chuyển mới"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleMethodSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="methodName">Tên phương thức *</Label>
                <Input
                  id="methodName"
                  value={methodFormData.name}
                  onChange={(e) =>
                    setMethodFormData({
                      ...methodFormData,
                      name: e.target.value,
                    })
                  }
                  placeholder="Nhập tên phương thức"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="methodZone">Khu vực *</Label>
                <Select
                  value={methodFormData.zoneId}
                  onValueChange={(value) =>
                    setMethodFormData({ ...methodFormData, zoneId: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn khu vực" />
                  </SelectTrigger>
                  <SelectContent>
                    {zones
                      ?.filter((z) => z.isActive)
                      .map((zone) => (
                        <SelectItem key={zone.id} value={zone.id}>
                          {zone.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="methodDescription">Mô tả</Label>
              <Textarea
                id="methodDescription"
                value={methodFormData.description}
                onChange={(e) =>
                  setMethodFormData({
                    ...methodFormData,
                    description: e.target.value,
                  })
                }
                placeholder="Nhập mô tả phương thức"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="methodType">Loại phương thức *</Label>
                <Select
                  value={methodFormData.type}
                  onValueChange={(value) =>
                    setMethodFormData({ ...methodFormData, type: value as any })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="STANDARD">Tiêu chuẩn</SelectItem>
                    <SelectItem value="EXPRESS">Nhanh</SelectItem>
                    <SelectItem value="SAME_DAY">Trong ngày</SelectItem>
                    <SelectItem value="PICKUP">Nhận tại cửa hàng</SelectItem>
                    <SelectItem value="CUSTOM">Tùy chỉnh</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="estimatedDays">Thời gian giao hàng *</Label>
                <Input
                  id="estimatedDays"
                  value={methodFormData.estimatedDays}
                  onChange={(e) =>
                    setMethodFormData({
                      ...methodFormData,
                      estimatedDays: e.target.value,
                    })
                  }
                  placeholder="VD: 2-3 ngày"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="baseFee">Phí giao hàng (VND) *</Label>
                <Input
                  id="baseFee"
                  type="number"
                  value={methodFormData.baseFee}
                  onChange={(e) =>
                    setMethodFormData({
                      ...methodFormData,
                      baseFee: Number(e.target.value),
                    })
                  }
                  placeholder="Nhập phí giao hàng"
                  min="0"
                  step="1000"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="freeShippingMin">Miễn phí từ (VND)</Label>
                <Input
                  id="freeShippingMin"
                  type="number"
                  value={methodFormData.freeShippingMin}
                  onChange={(e) =>
                    setMethodFormData({
                      ...methodFormData,
                      freeShippingMin: e.target.value,
                    })
                  }
                  placeholder="Không giới hạn"
                  min="0"
                  step="1000"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxWeight">Trọng lượng tối đa (kg)</Label>
              <Input
                id="maxWeight"
                type="number"
                value={methodFormData.maxWeight}
                onChange={(e) =>
                  setMethodFormData({
                    ...methodFormData,
                    maxWeight: e.target.value,
                  })
                }
                placeholder="Không giới hạn"
                min="0"
                step="0.1"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="methodIsActive"
                checked={methodFormData.isActive}
                onCheckedChange={(checked) =>
                  setMethodFormData({ ...methodFormData, isActive: checked })
                }
              />
              <Label htmlFor="methodIsActive">Kích hoạt phương thức</Label>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateMethodDialogOpen(false);
                  setIsEditMethodDialogOpen(false);
                  setSelectedMethod(null);
                  resetMethodForm();
                }}
              >
                Hủy
              </Button>
              <Button type="submit">
                {selectedMethod ? "Cập nhật" : "Tạo mới"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
