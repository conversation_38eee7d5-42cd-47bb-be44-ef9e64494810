import NextAuth from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
      avatar?: string;
      type?: "user" | "admin";
      department?: string;
      permissions?: Record<string, boolean>;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: string;
    avatar?: string;
    type?: "user" | "admin";
    department?: string;
    permissions?: Record<string, boolean>;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    type?: "user" | "admin";
    department?: string;
    permissions?: Record<string, boolean>;
  }
}
