'use client';

import Link from 'next/link';
import { Heart, ShoppingCart, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils';

const featuredProducts = [
	{
		id: 1,
		name: 'Áo thun cotton premium',
		slug: 'ao-thun-cotton-premium',
		price: 299000,
		salePrice: 199000,
		image: '/images/products/t-shirt-1.jpg',
		rating: 4.8,
		reviews: 124,
		isNew: true,
		isSale: true,
	},
	{
		id: 2,
		name: '<PERSON><PERSON>y maxi hoa nhí',
		slug: 'vay-maxi-hoa-nhi',
		price: 599000,
		salePrice: null,
		image: '/images/products/dress-1.jpg',
		rating: 4.9,
		reviews: 89,
		isNew: false,
		isSale: false,
	},
	{
		id: 3,
		name: 'Quần jeans skinny',
		slug: 'quan-jeans-skinny',
		price: 799000,
		salePrice: 599000,
		image: '/images/products/jeans-1.jpg',
		rating: 4.7,
		reviews: 156,
		isNew: false,
		isSale: true,
	},
	{
		id: 4,
		name: '<PERSON><PERSON> <PERSON>ho<PERSON> bomber',
		slug: 'ao-khoac-bomber',
		price: 899000,
		salePrice: null,
		image: '/images/products/jacket-1.jpg',
		rating: 4.6,
		reviews: 67,
		isNew: true,
		isSale: false,
	},
	{
		id: 5,
		name: 'Túi xách da thật',
		slug: 'tui-xach-da-that',
		price: 1299000,
		salePrice: 999000,
		image: '/images/products/bag-1.jpg',
		rating: 4.9,
		reviews: 203,
		isNew: false,
		isSale: true,
	},
	{
		id: 6,
		name: 'Giày sneaker trắng',
		slug: 'giay-sneaker-trang',
		price: 1199000,
		salePrice: null,
		image: '/images/products/shoes-1.jpg',
		rating: 4.8,
		reviews: 178,
		isNew: true,
		isSale: false,
	},
];

export function FeaturedProducts() {
	return (
		<section className="py-16 lg:py-24 bg-muted/30">
			<div className="container mx-auto px-4">
				{/* Header */}
				<div className="text-center mb-12">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4">
						Sản phẩm nổi bật
					</h2>
					<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
						Khám phá những sản phẩm được yêu thích nhất và có đánh giá cao từ khách hàng
					</p>
				</div>

				{/* Products Grid */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
					{featuredProducts.map((product) => (
						<Card key={product.id} className="group overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300">
							<CardContent className="p-0">
								{/* Product Image */}
								<div className="relative overflow-hidden bg-muted">
									<div className="aspect-[4/5] bg-gradient-to-br from-muted to-muted-foreground/10 flex items-center justify-center">
										<div className="text-4xl font-bold text-muted-foreground/20">
											{product.name.charAt(0)}
										</div>
									</div>
									
									{/* Badges */}
									<div className="absolute top-3 left-3 flex flex-col gap-2">
										{product.isNew && (
											<span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
												Mới
											</span>
										)}
										{product.isSale && (
											<span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
												Sale
											</span>
										)}
									</div>

									{/* Actions */}
									<div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
										<Button size="icon" variant="secondary" className="h-8 w-8">
											<Heart className="h-4 w-4" />
										</Button>
									</div>

									{/* Quick Add to Cart */}
									<div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
										<Button className="w-full" size="sm">
											<ShoppingCart className="h-4 w-4 mr-2" />
											Thêm vào giỏ
										</Button>
									</div>
								</div>

								{/* Product Info */}
								<div className="p-4">
									<Link href={`/products/${product.slug}`}>
										<h3 className="font-semibold text-sm lg:text-base mb-2 hover:text-primary transition-colors line-clamp-2">
											{product.name}
										</h3>
									</Link>

									{/* Rating */}
									<div className="flex items-center gap-1 mb-2">
										<div className="flex items-center">
											{[...Array(5)].map((_, i) => (
												<Star
													key={i}
													className={`h-3 w-3 ${
														i < Math.floor(product.rating)
															? 'text-yellow-400 fill-current'
															: 'text-gray-300'
													}`}
												/>
											))}
										</div>
										<span className="text-xs text-muted-foreground">
											{product.rating} ({product.reviews})
										</span>
									</div>

									{/* Price */}
									<div className="flex items-center gap-2">
										{product.salePrice ? (
											<>
												<span className="font-bold text-primary">
													{formatCurrency(product.salePrice)}
												</span>
												<span className="text-sm text-muted-foreground line-through">
													{formatCurrency(product.price)}
												</span>
											</>
										) : (
											<span className="font-bold text-primary">
												{formatCurrency(product.price)}
											</span>
										)}
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>

				{/* View All Button */}
				<div className="text-center mt-12">
					<Button asChild variant="outline" size="lg">
						<Link href="/products">
							Xem tất cả sản phẩm
						</Link>
					</Button>
				</div>
			</div>
		</section>
	);
}
