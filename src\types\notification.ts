export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  targetType: NotificationTarget;
  targetId?: string;
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
  metadata?: any;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
}

export type NotificationType =
  | "INFO"
  | "SUCCESS"
  | "WARNING"
  | "ERROR"
  | "SYSTEM";

export type NotificationPriority = "LOW" | "NORMAL" | "HIGH" | "URGENT";

export type NotificationTarget =
  | "ALL_ADMINS"
  | "SPECIFIC_ADMIN"
  | "ROLE_ADMIN"
  | "ROLE_MODERATOR";

export interface NotificationRule {
  id: string;
  name: string;
  description: string;
  eventType: string;
  conditions: Record<string, any>;
  notificationTemplate: {
    title: string;
    message: string;
    type: NotificationType;
    priority: NotificationPriority;
    targetType: NotificationTarget;
    targetId?: string;
    actionUrl?: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationEvent {
  type: string;
  data: Record<string, any>;
  triggeredBy?: string;
  timestamp: Date;
}

export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  markAsRead: (notificationId: string) => Promise<void>;
  markAsUnread: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
  createNotification: (notification: Partial<Notification>) => Promise<void>;
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  targetType?: NotificationTarget;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
}

export interface BulkNotificationAction {
  action: "mark_read" | "mark_unread" | "delete";
  notificationIds: string[];
}

export interface NotificationFilters {
  search: string;
  type: string;
  priority: string;
  isRead: string;
}
