"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { SiteSettings } from "@/hooks/useSettings";

interface SettingsContextType {
  settings: SiteSettings;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

const DEFAULT_SETTINGS: SiteSettings = {
  siteName: "NS Shop",
  siteDescription: "Cửa hàng thời trang trực tuyến",
  siteUrl: "https://nsshop.com",
  logo: "",
  favicon: "",
  contactEmail: "<EMAIL>",
  contactPhone: "**********",
  address: "123 Đường ABC, Quận 1, TP.HCM",
  socialMedia: {
    facebook: "",
    instagram: "",
    twitter: "",
  },
  paymentMethods: {
    cod: true,
    bankTransfer: true,
    creditCard: false,
  },
  shippingSettings: {
    freeShippingThreshold: 500000,
    shippingFee: 30000,
    estimatedDelivery: "2-3 ngày",
  },
  emailSettings: {
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "NS Shop",
  },
  notifications: {
    orderNotifications: true,
    stockAlerts: true,
    customerNotifications: true,
  },
  seoSettings: {
    metaTitle: "NS Shop - Thời trang trực tuyến",
    metaDescription:
      "Cửa hàng thời trang trực tuyến với những sản phẩm chất lượng cao",
    metaKeywords: "thời trang, quần áo, giày dép, phụ kiện",
    googleAnalytics: "",
    facebookPixel: "",
  },
  securitySettings: {
    enableTwoFactor: false,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    enableCaptcha: false,
  },
};

const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined
);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<SiteSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(false); // Start with false to avoid blocking UI
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch("/api/settings", {
        cache: "no-store",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(
          "Settings API response not ok:",
          response.status,
          response.statusText
        );
        throw new Error(`Failed to fetch settings: ${response.status}`);
      }

      const data = await response.json();
      console.log("Settings fetched successfully:", data);
      setSettings({ ...DEFAULT_SETTINGS, ...data });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      console.error("Error fetching settings:", err);
      // Keep default settings on error
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setLoading(false);
    }
  };

  const refreshSettings = async () => {
    await fetchSettings();
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const value: SettingsContextType = {
    settings,
    loading,
    error,
    refreshSettings,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettingsContext() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error(
      "useSettingsContext must be used within a SettingsProvider"
    );
  }
  return context;
}
