'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PageForm } from '@/components/admin/PageForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface PageFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  featured: boolean;
  featuredImage?: string;
  metaTitle?: string;
  metaDescription?: string;
}

export default function CreatePagePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: PageFormData) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Tạo trang thành công');
        
        // Redirect based on status
        if (data.status === 'PUBLISHED') {
          router.push('/admin/pages');
        } else {
          router.push('/admin/pages');
        }
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi tạo trang');
      }
    } catch (error) {
      console.error('Create page error:', error);
      toast.error('Có lỗi xảy ra khi tạo trang');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/pages">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Tạo trang mới</h1>
            <p className="text-muted-foreground">
              Tạo trang tĩnh mới cho website
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <PageForm 
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </div>
  );
}
