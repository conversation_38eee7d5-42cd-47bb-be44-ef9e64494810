import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { logAdminAction, getRequestMetadata } from "@/lib/audit-logger";

/**
 * Higher-order function to wrap API routes with audit logging
 */
export function withAudit(
  action: string,
  resource: string,
  options: {
    getResourceId?: (
      request: NextRequest,
      params?: any,
      body?: any
    ) => string | undefined;
    getOldValues?: (
      request: NextRequest,
      params?: any,
      body?: any
    ) => Promise<Record<string, any> | undefined>;
    getNewValues?: (
      request: NextRequest,
      params?: any,
      body?: any
    ) => Record<string, any> | undefined;
    getDescription?: (
      request: NextRequest,
      params?: any,
      body?: any
    ) => string | undefined;
    skipLogging?: (request: NextRequest, params?: any, body?: any) => boolean;
  } = {}
) {
  return function (
    handler: (request: NextRequest, context?: any) => Promise<NextResponse>
  ) {
    return async function (
      request: NextRequest,
      context?: any
    ): Promise<NextResponse> {
      let body: any = null;
      let params: any = context?.params;

      try {
        // Get admin session
        const session = await getServerSession(adminAuthOptions);
        if (!session?.user || session.user.type !== "admin") {
          return handler(request, context);
        }

        // Skip logging if specified
        if (options.skipLogging?.(request, params, body)) {
          return handler(request, context);
        }

        const { ipAddress, userAgent } = getRequestMetadata(request);

        // Try to parse request body for POST/PUT/PATCH requests
        if (["POST", "PUT", "PATCH"].includes(request.method)) {
          try {
            const clonedRequest = request.clone();
            body = await clonedRequest.json();
          } catch {
            // Body might not be JSON or already consumed
          }
        }

        // Get old values before operation (for updates)
        let oldValues: Record<string, any> | undefined;
        if (options.getOldValues) {
          oldValues = await options.getOldValues(request, params, body);
        }

        // Execute the original handler
        const result = await handler(request, context);

        // Only log if the operation was successful (2xx status codes)
        if (result.status >= 200 && result.status < 300) {
          await logAdminAction({
            action,
            resource,
            resourceId: options.getResourceId?.(request, params, body),
            oldValues,
            newValues: options.getNewValues?.(request, params, body),
            description: options.getDescription?.(request, params, body),
            adminId: session.user.id,
            ipAddress,
            userAgent,
          });
        }

        return result;
      } catch (error) {
        // Log failed operations
        const session = await getServerSession(adminAuthOptions);
        if (session?.user && session.user.type === "admin") {
          const { ipAddress, userAgent } = getRequestMetadata(request);

          await logAdminAction({
            action: `${action}_FAILED`,
            resource,
            resourceId: options.getResourceId?.(request, params, body),
            description: `Failed to ${action.toLowerCase()} ${resource}: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
            adminId: session.user.id,
            ipAddress,
            userAgent,
          });
        }

        throw error;
      }
    };
  };
}

/**
 * Predefined audit configurations for common operations
 */
export const auditConfigs = {
  // Product operations
  createProduct: {
    action: "CREATE",
    resource: "Product",
    getResourceId: (_: NextRequest, __: any, body: any) => body?.id,
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, __: any, body: any) =>
      `Created product: ${body?.name || "Unknown"}`,
  },

  updateProduct: {
    action: "UPDATE",
    resource: "Product",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const product = await prisma.product.findUnique({
        where: { id: params.id },
      });
      return product || undefined;
    },
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, params: any, body: any) =>
      `Updated product: ${body?.name || params?.id}`,
  },

  deleteProduct: {
    action: "DELETE",
    resource: "Product",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const product = await prisma.product.findUnique({
        where: { id: params.id },
      });
      return product || undefined;
    },
    getDescription: (_: NextRequest, params: any) =>
      `Deleted product: ${params?.id}`,
  },

  // Category operations
  createCategory: {
    action: "CREATE",
    resource: "Category",
    getResourceId: (_: NextRequest, __: any, body: any) => body?.id,
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, __: any, body: any) =>
      `Created category: ${body?.name || "Unknown"}`,
  },

  updateCategory: {
    action: "UPDATE",
    resource: "Category",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const category = await prisma.category.findUnique({
        where: { id: params.id },
      });
      return category || undefined;
    },
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, params: any, body: any) =>
      `Updated category: ${body?.name || params?.id}`,
  },

  deleteCategory: {
    action: "DELETE",
    resource: "Category",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const category = await prisma.category.findUnique({
        where: { id: params.id },
      });
      return category || undefined;
    },
    getDescription: (_: NextRequest, params: any) =>
      `Deleted category: ${params?.id}`,
  },

  // Order operations
  updateOrder: {
    action: "UPDATE",
    resource: "Order",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const order = await prisma.order.findUnique({ where: { id: params.id } });
      return order || undefined;
    },
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, params: any, body: any) =>
      `Updated order ${params?.id}: ${body?.status ? `status to ${body.status}` : "details"}`,
  },

  // User operations
  updateUser: {
    action: "UPDATE",
    resource: "User",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const user = await prisma.user.findUnique({ where: { id: params.id } });
      return user || undefined;
    },
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: (_: NextRequest, params: any, body: any) =>
      `Updated user: ${body?.name || params?.id}`,
  },

  // Settings operations
  updateSettings: {
    action: "UPDATE",
    resource: "Settings",
    getResourceId: () => "site-settings",
    getNewValues: (_: NextRequest, __: any, body: any) => body,
    getDescription: () => "Updated site settings",
  },

  // Admin user operations
  createAdmin: {
    action: "CREATE",
    resource: "AdminUser",
    getResourceId: (_: NextRequest, __: any, body: any) => body?.id,
    getNewValues: (_: NextRequest, __: any, body: any) => {
      const { password: _password, ...safeBody } = body || {};
      return safeBody;
    },
    getDescription: (_: NextRequest, __: any, body: any) =>
      `Created admin user: ${body?.name || "Unknown"}`,
  },

  updateAdmin: {
    action: "UPDATE",
    resource: "AdminUser",
    getResourceId: (_: NextRequest, params: any) => params?.id,
    getOldValues: async (_: NextRequest, params: any) => {
      if (!params?.id) return undefined;
      const { prisma } = await import("@/lib/prisma");
      const admin = await prisma.adminUser.findUnique({
        where: { id: params.id },
      });
      if (admin) {
        const { password: _password, ...safeAdmin } = admin;
        return safeAdmin;
      }
      return undefined;
    },
    getNewValues: (_: NextRequest, __: any, body: any) => {
      const { password: _password, ...safeBody } = body || {};
      return safeBody;
    },
    getDescription: (_: NextRequest, params: any, body: any) =>
      `Updated admin user: ${body?.name || params?.id}`,
  },
};

/**
 * Helper function to create audit-enabled route handlers
 */
export function createAuditRoute(
  config: keyof typeof auditConfigs,
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  const auditConfig = auditConfigs[config];
  return withAudit(
    auditConfig.action,
    auditConfig.resource,
    auditConfig
  )(handler);
}
