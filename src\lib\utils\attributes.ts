import {
  Attribute,
  AttributeValue,
  AttributeType,
  ProductAttribute,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

/**
 * Generate slug from Vietnamese text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-")
    .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
    .replace(/[èéẹẻẽêềếệểễ]/g, "e")
    .replace(/[ìíịỉĩ]/g, "i")
    .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
    .replace(/[ùúụủ<PERSON>]/g, "u")
    .replace(/[ỳýỵỷỹ]/g, "y")
    .replace(/[đ]/g, "d")
    .replace(/[^a-z0-9-]/g, "")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
}

/**
 * Check if attribute type needs values
 */
export function needsValues(type: AttributeType): boolean {
  return ["COLOR", "SIZE", "SELECT", "MULTI_SELECT"].includes(type);
}

/**
 * Check if attribute type supports multiple values
 */
export function supportsMultipleValues(type: AttributeType): boolean {
  return type === "MULTI_SELECT";
}

/**
 * Get color code for color attribute values
 */
export function getColorCode(colorName: string): string {
  const colorMap: Record<string, string> = {
    "đỏ": "#ef4444",
    "red": "#ef4444",
    "xanh dương": "#3b82f6",
    "blue": "#3b82f6",
    "xanh lá": "#22c55e",
    "green": "#22c55e",
    "vàng": "#eab308",
    "yellow": "#eab308",
    "đen": "#000000",
    "black": "#000000",
    "trắng": "#ffffff",
    "white": "#ffffff",
    "hồng": "#ec4899",
    "pink": "#ec4899",
    "tím": "#a855f7",
    "purple": "#a855f7",
    "xám": "#6b7280",
    "gray": "#6b7280",
    "grey": "#6b7280",
    "nâu": "#a3a3a3",
    "brown": "#a3a3a3",
    "cam": "#f97316",
    "orange": "#f97316",
    "be": "#f5f5dc",
    "beige": "#f5f5dc",
    "navy": "#1e3a8a",
    "khaki": "#f0e68c",
  };
  
  return colorMap[colorName.toLowerCase()] || "#6b7280";
}

/**
 * Validate attribute data
 */
export function validateAttribute(data: {
  name: string;
  slug?: string;
  type: AttributeType;
  values?: { value: string; slug?: string }[];
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate name
  if (!data.name || data.name.trim().length === 0) {
    errors.push("Tên thuộc tính là bắt buộc");
  }

  // Validate slug
  if (data.slug && !/^[a-z0-9-]+$/.test(data.slug)) {
    errors.push("Slug chỉ được chứa chữ cái thường, số và dấu gạch ngang");
  }

  // Validate values for types that need them
  if (needsValues(data.type)) {
    if (!data.values || data.values.length === 0) {
      errors.push(`Loại thuộc tính ${ATTRIBUTE_TYPE_LABELS[data.type]} cần ít nhất một giá trị`);
    } else {
      // Check for duplicate values
      const valueNames = data.values.map(v => v.value.toLowerCase());
      const uniqueValues = new Set(valueNames);
      if (valueNames.length !== uniqueValues.size) {
        errors.push("Không được có giá trị trùng lặp");
      }

      // Check for empty values
      const emptyValues = data.values.filter(v => !v.value || v.value.trim().length === 0);
      if (emptyValues.length > 0) {
        errors.push("Tất cả giá trị phải có nội dung");
      }

      // Check for duplicate slugs
      const valueSlugs = data.values
        .map(v => v.slug || generateSlug(v.value))
        .filter(Boolean);
      const uniqueSlugs = new Set(valueSlugs);
      if (valueSlugs.length !== uniqueSlugs.size) {
        errors.push("Không được có slug giá trị trùng lặp");
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Sort attributes by sort order and name
 */
export function sortAttributes(attributes: Attribute[]): Attribute[] {
  return [...attributes].sort((a, b) => {
    if (a.sortOrder !== b.sortOrder) {
      return a.sortOrder - b.sortOrder;
    }
    return a.name.localeCompare(b.name, "vi");
  });
}

/**
 * Sort attribute values by sort order and value
 */
export function sortAttributeValues(values: AttributeValue[]): AttributeValue[] {
  return [...values].sort((a, b) => {
    if (a.sortOrder !== b.sortOrder) {
      return a.sortOrder - b.sortOrder;
    }
    return a.value.localeCompare(b.value, "vi");
  });
}

/**
 * Filter attributes by search term
 */
export function filterAttributes(
  attributes: Attribute[],
  searchTerm: string
): Attribute[] {
  if (!searchTerm) return attributes;

  const term = searchTerm.toLowerCase();
  return attributes.filter(
    (attr) =>
      attr.name.toLowerCase().includes(term) ||
      attr.slug.toLowerCase().includes(term) ||
      attr.description?.toLowerCase().includes(term) ||
      ATTRIBUTE_TYPE_LABELS[attr.type].toLowerCase().includes(term)
  );
}

/**
 * Group attributes by type
 */
export function groupAttributesByType(
  attributes: Attribute[]
): Record<AttributeType, Attribute[]> {
  const groups: Record<AttributeType, Attribute[]> = {
    TEXT: [],
    NUMBER: [],
    COLOR: [],
    SIZE: [],
    BOOLEAN: [],
    SELECT: [],
    MULTI_SELECT: [],
  };

  attributes.forEach((attr) => {
    groups[attr.type].push(attr);
  });

  return groups;
}

/**
 * Get attribute statistics
 */
export function getAttributeStats(attributes: Attribute[]) {
  const total = attributes.length;
  const byType = groupAttributesByType(attributes);
  const withValues = attributes.filter(attr => attr._count?.values && attr._count.values > 0).length;
  const withoutValues = total - withValues;
  const required = attributes.filter(attr => attr.isRequired).length;
  const filterable = attributes.filter(attr => attr.isFilterable).length;

  return {
    total,
    byType: Object.fromEntries(
      Object.entries(byType).map(([type, attrs]) => [type, attrs.length])
    ) as Record<AttributeType, number>,
    withValues,
    withoutValues,
    required,
    filterable,
  };
}

/**
 * Format attribute value for display
 */
export function formatAttributeValue(
  attribute: Attribute,
  value: AttributeValue | string
): string {
  const valueStr = typeof value === "string" ? value : value.value;

  switch (attribute.type) {
    case "BOOLEAN":
      return valueStr === "true" ? "Có" : "Không";
    case "NUMBER":
      return valueStr;
    default:
      return valueStr;
  }
}

/**
 * Check if attribute can be deleted
 */
export function canDeleteAttribute(attribute: Attribute): {
  canDelete: boolean;
  reason?: string;
} {
  if (attribute._count?.products && attribute._count.products > 0) {
    return {
      canDelete: false,
      reason: `Thuộc tính đang được sử dụng bởi ${attribute._count.products} sản phẩm`,
    };
  }

  return { canDelete: true };
}

/**
 * Check if attribute value can be deleted
 */
export function canDeleteAttributeValue(value: AttributeValue): {
  canDelete: boolean;
  reason?: string;
} {
  if (value._count?.products && value._count.products > 0) {
    return {
      canDelete: false,
      reason: `Giá trị đang được sử dụng bởi ${value._count.products} sản phẩm`,
    };
  }

  return { canDelete: true };
}

/**
 * Convert product attributes to display format
 */
export function formatProductAttributes(
  productAttributes: ProductAttribute[]
): Record<string, { attribute: Attribute; values: AttributeValue[] }> {
  const grouped: Record<string, { attribute: Attribute; values: AttributeValue[] }> = {};

  productAttributes.forEach((pa) => {
    if (!pa.attribute || !pa.attributeValue) return;

    const attrId = pa.attribute.id;
    if (!grouped[attrId]) {
      grouped[attrId] = {
        attribute: pa.attribute,
        values: [],
      };
    }

    grouped[attrId].values.push(pa.attributeValue);
  });

  return grouped;
}

/**
 * Generate attribute combinations for product variants
 */
export function generateAttributeCombinations(
  attributes: Record<string, string[]>
): Record<string, string>[] {
  const keys = Object.keys(attributes);
  if (keys.length === 0) return [];

  const combinations: Record<string, string>[] = [];

  function generate(index: number, current: Record<string, string>) {
    if (index === keys.length) {
      combinations.push({ ...current });
      return;
    }

    const key = keys[index];
    const values = attributes[key];

    for (const value of values) {
      current[key] = value;
      generate(index + 1, current);
    }
  }

  generate(0, {});
  return combinations;
}

/**
 * Validate attribute selection for product
 */
export function validateAttributeSelection(
  attributes: Attribute[],
  selection: Record<string, string | string[]>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required attributes
  const requiredAttributes = attributes.filter(attr => attr.isRequired);
  for (const attr of requiredAttributes) {
    const value = selection[attr.id];
    if (!value || (Array.isArray(value) && value.length === 0)) {
      errors.push(`${attr.name} là thuộc tính bắt buộc`);
    }
  }

  // Validate value types
  for (const [attrId, value] of Object.entries(selection)) {
    const attribute = attributes.find(attr => attr.id === attrId);
    if (!attribute) continue;

    if (attribute.type === "MULTI_SELECT") {
      if (!Array.isArray(value)) {
        errors.push(`${attribute.name} phải là mảng giá trị`);
      }
    } else {
      if (Array.isArray(value)) {
        errors.push(`${attribute.name} chỉ được chọn một giá trị`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
