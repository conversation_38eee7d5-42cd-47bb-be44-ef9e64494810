/**
 * Utility functions for exporting data to various formats
 */

export interface ExportData {
  [key: string]: any;
}

export interface ExportOptions {
  filename?: string;
  headers?: Record<string, string>;
  dateFormat?: string;
  timezone?: string;
}

/**
 * Generate CSV content from data array
 */
export function generateCSV(
  data: ExportData[],
  options: ExportOptions = {}
): string {
  if (data.length === 0) {
    return "";
  }

  const { headers = {}, dateFormat = "dd/MM/yyyy HH:mm:ss" } = options;

  // Get all unique keys from data
  const allKeys = Array.from(
    new Set(data.flatMap(item => Object.keys(item)))
  );

  // Create header row
  const headerRow = allKeys.map(key => headers[key] || key);
  const csvRows = [headerRow.join(",")];

  // Process data rows
  for (const item of data) {
    const row = allKeys.map(key => {
      let value = item[key];

      // Handle null/undefined values
      if (value === null || value === undefined) {
        return '""';
      }

      // Handle dates
      if (value instanceof Date) {
        value = value.toLocaleString("vi-VN", {
          timeZone: "Asia/Ho_Chi_Minh",
        });
      }

      // Handle objects/arrays
      if (typeof value === "object") {
        value = JSON.stringify(value);
      }

      // Convert to string and escape quotes
      const stringValue = String(value).replace(/"/g, '""');
      
      // Wrap in quotes if contains comma, newline, or quote
      if (stringValue.includes(",") || stringValue.includes("\n") || stringValue.includes('"')) {
        return `"${stringValue}"`;
      }

      return stringValue;
    });

    csvRows.push(row.join(","));
  }

  return csvRows.join("\n");
}

/**
 * Download CSV file
 */
export function downloadCSV(
  data: ExportData[],
  options: ExportOptions = {}
): void {
  const { filename = `export-${new Date().toISOString().split('T')[0]}.csv` } = options;
  
  const csvContent = generateCSV(data, options);
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  
  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * Prepare Excel data for frontend processing
 * Returns data in a format that can be used with libraries like xlsx
 */
export function prepareExcelData(
  data: ExportData[],
  options: ExportOptions = {}
): {
  data: any[][];
  filename: string;
  headers: string[];
} {
  if (data.length === 0) {
    return {
      data: [],
      filename: options.filename || `export-${new Date().toISOString().split('T')[0]}.xlsx`,
      headers: [],
    };
  }

  const { headers = {}, filename = `export-${new Date().toISOString().split('T')[0]}.xlsx` } = options;

  // Get all unique keys from data
  const allKeys = Array.from(
    new Set(data.flatMap(item => Object.keys(item)))
  );

  // Create header row
  const headerRow = allKeys.map(key => headers[key] || key);

  // Process data rows
  const dataRows = data.map(item => {
    return allKeys.map(key => {
      let value = item[key];

      // Handle null/undefined values
      if (value === null || value === undefined) {
        return "";
      }

      // Handle dates
      if (value instanceof Date) {
        return value.toLocaleString("vi-VN", {
          timeZone: "Asia/Ho_Chi_Minh",
        });
      }

      // Handle objects/arrays
      if (typeof value === "object") {
        return JSON.stringify(value);
      }

      return String(value);
    });
  });

  return {
    data: [headerRow, ...dataRows],
    filename,
    headers: headerRow,
  };
}

/**
 * Format audit log data for export
 */
export function formatAuditLogsForExport(auditLogs: any[]): ExportData[] {
  return auditLogs.map(log => ({
    id: log.id,
    action: log.action,
    resource: log.resource,
    resourceId: log.resourceId || "",
    description: log.description || "",
    adminName: log.admin?.name || "",
    adminEmail: log.admin?.email || "",
    adminRole: log.admin?.role || "",
    ipAddress: log.ipAddress || "",
    userAgent: log.userAgent || "",
    createdAt: new Date(log.createdAt),
    oldValues: log.oldValues ? JSON.stringify(log.oldValues, null, 2) : "",
    newValues: log.newValues ? JSON.stringify(log.newValues, null, 2) : "",
  }));
}

/**
 * Get audit logs export headers in Vietnamese
 */
export function getAuditLogsExportHeaders(): Record<string, string> {
  return {
    id: "ID",
    action: "Hành động",
    resource: "Tài nguyên",
    resourceId: "ID Tài nguyên",
    description: "Mô tả",
    adminName: "Tên Admin",
    adminEmail: "Email Admin",
    adminRole: "Vai trò Admin",
    ipAddress: "Địa chỉ IP",
    userAgent: "User Agent",
    createdAt: "Thời gian",
    oldValues: "Giá trị cũ",
    newValues: "Giá trị mới",
  };
}

/**
 * Export audit logs to CSV
 */
export function exportAuditLogsToCSV(
  auditLogs: any[],
  filename?: string
): void {
  const data = formatAuditLogsForExport(auditLogs);
  const headers = getAuditLogsExportHeaders();
  
  downloadCSV(data, {
    filename: filename || `audit-logs-${new Date().toISOString().split('T')[0]}.csv`,
    headers,
  });
}

/**
 * Prepare audit logs for Excel export
 */
export function prepareAuditLogsForExcel(
  auditLogs: any[],
  filename?: string
): {
  data: any[][];
  filename: string;
  headers: string[];
} {
  const data = formatAuditLogsForExport(auditLogs);
  const headers = getAuditLogsExportHeaders();
  
  return prepareExcelData(data, {
    filename: filename || `audit-logs-${new Date().toISOString().split('T')[0]}.xlsx`,
    headers,
  });
}

/**
 * Progress tracking for large exports
 */
export class ExportProgress {
  private total: number;
  private current: number = 0;
  private onProgress?: (progress: number) => void;

  constructor(total: number, onProgress?: (progress: number) => void) {
    this.total = total;
    this.onProgress = onProgress;
  }

  increment(): void {
    this.current++;
    this.updateProgress();
  }

  setProgress(current: number): void {
    this.current = current;
    this.updateProgress();
  }

  private updateProgress(): void {
    const progress = Math.round((this.current / this.total) * 100);
    this.onProgress?.(progress);
  }

  getProgress(): number {
    return Math.round((this.current / this.total) * 100);
  }

  isComplete(): boolean {
    return this.current >= this.total;
  }
}

/**
 * Chunk data for processing large datasets
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Process large export with progress tracking
 */
export async function processLargeExport<T>(
  data: T[],
  processor: (chunk: T[]) => Promise<any>,
  options: {
    chunkSize?: number;
    onProgress?: (progress: number) => void;
    delay?: number;
  } = {}
): Promise<any[]> {
  const { chunkSize = 100, onProgress, delay = 10 } = options;
  
  const chunks = chunkArray(data, chunkSize);
  const progress = new ExportProgress(chunks.length, onProgress);
  const results: any[] = [];

  for (const chunk of chunks) {
    const result = await processor(chunk);
    results.push(result);
    progress.increment();
    
    // Add small delay to prevent blocking UI
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return results;
}
