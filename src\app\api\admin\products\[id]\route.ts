import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";

const updateProductSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  price: z.number().positive().optional(),
  salePrice: z.number().positive().optional(),
  images: z.array(z.string()).optional(),
  imageTypes: z.array(z.enum(["INTERNAL", "EXTERNAL"])).optional(),
  externalImages: z.array(z.string()).optional(),
  categoryId: z.string().optional(),
  brandId: z.string().optional(),

  stock: z.number().int().min(0).optional(),
  sku: z.string().optional(),
  slug: z.string().optional(),
  featured: z.boolean().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "OUT_OF_STOCK"]).optional(),
  tags: z.array(z.string()).optional(),
  attributes: z
    .array(
      z.object({
        attributeId: z.string(),
        attributeValueId: z.string(),
      })
    )
    .optional(),
});

// GET /api/admin/products/[id] - Lấy chi tiết sản phẩm
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        brand: true,

        ProductAttribute: {
          include: {
            attribute: {
              include: {
                values: true,
              },
            },
            attributeValue: true,
          },
        },
        reviews: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error("Get product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin sản phẩm" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/products/[id] - Cập nhật sản phẩm
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Enhanced validation
    const validationErrors: string[] = [];

    if (body.name !== undefined) {
      if (!body.name || body.name.trim().length === 0) {
        validationErrors.push("Tên sản phẩm không được để trống");
      } else if (body.name.length < 3) {
        validationErrors.push("Tên sản phẩm phải có ít nhất 3 ký tự");
      } else if (body.name.length > 255) {
        validationErrors.push("Tên sản phẩm không được vượt quá 255 ký tự");
      }
    }

    if (body.description !== undefined) {
      if (!body.description || body.description.trim().length === 0) {
        validationErrors.push("Mô tả sản phẩm không được để trống");
      } else if (body.description.length < 10) {
        validationErrors.push("Mô tả sản phẩm phải có ít nhất 10 ký tự");
      }
    }

    if (body.price !== undefined) {
      if (!body.price || parseFloat(body.price) <= 0) {
        validationErrors.push("Giá sản phẩm phải lớn hơn 0");
      } else if (parseFloat(body.price) > 999999999) {
        validationErrors.push(
          "Giá sản phẩm không được vượt quá 999,999,999 VND"
        );
      }
    }

    if (body.salePrice !== undefined && body.salePrice !== null) {
      if (body.price && parseFloat(body.salePrice) >= parseFloat(body.price)) {
        validationErrors.push("Giá khuyến mãi phải nhỏ hơn giá gốc");
      }
    }

    if (body.stock !== undefined && parseInt(body.stock) < 0) {
      validationErrors.push("Số lượng tồn kho không được âm");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: validationErrors },
        { status: 400 }
      );
    }

    const data = updateProductSchema.parse(body);

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    // Check SKU uniqueness if updating
    if (data.sku && data.sku !== existingProduct.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: data.sku },
      });

      if (existingSku) {
        return NextResponse.json({ error: "SKU đã tồn tại" }, { status: 400 });
      }
    }

    // Update slug if name is changed
    const updateData = { ...data };
    if (data.name && data.name !== existingProduct.name) {
      const slug = data.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();

      let finalSlug = slug;
      let counter = 1;
      while (
        await prisma.product.findFirst({
          where: {
            slug: finalSlug,
            id: { not: params.id },
          },
        })
      ) {
        finalSlug = `${slug}-${counter}`;
        counter++;
      }

      updateData.slug = finalSlug;
    }

    // Update product with attributes in transaction
    const product = await prisma.$transaction(async (tx) => {
      // Extract attributes from updateData
      const { attributes, ...productData } = updateData;

      // Update the product
      const updatedProduct = await tx.product.update({
        where: { id: params.id },
        data: productData,
      });

      // Handle attributes update
      if (attributes !== undefined) {
        // Delete existing product attributes
        await tx.productAttribute.deleteMany({
          where: { productId: params.id },
        });

        // Create new product attributes
        if (attributes && attributes.length > 0) {
          await tx.productAttribute.createMany({
            data: attributes.map((attr) => ({
              productId: params.id,
              attributeId: attr.attributeId,
              attributeValueId: attr.attributeValueId,
            })),
          });
        }
      }

      return updatedProduct;
    });

    // Fetch updated product with relations
    const updatedProduct = await prisma.product.findUnique({
      where: { id: product.id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },

        ProductAttribute: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            attributeValue: {
              select: {
                id: true,
                value: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedProduct,
      message: "Sản phẩm đã được cập nhật thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật sản phẩm" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/products/[id] - Xóa sản phẩm
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    // Delete product
    await prisma.product.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Sản phẩm đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa sản phẩm" },
      { status: 500 }
    );
  }
}
