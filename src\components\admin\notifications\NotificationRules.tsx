"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Settings,
  Plus,
  Play,
  RefreshCw,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";
import { NotificationRule } from "@/types/notification";

export function NotificationRules() {
  const [rules, setRules] = useState<NotificationRule[]>([]);
  const [eventTypes, setEventTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState<NotificationRule | null>(
    null
  );

  useEffect(() => {
    loadRules();
  }, []);

  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/notifications/rules");
      if (response.ok) {
        const data = await response.json();
        setRules(data.rules);
        setEventTypes(data.eventTypes);
      } else {
        throw new Error("Failed to load rules");
      }
    } catch (error) {
      console.error("Error loading rules:", error);
      toast.error("Không thể tải danh sách rules");
    } finally {
      setLoading(false);
    }
  };

  const testRule = async (
    ruleId?: string,
    eventType?: string,
    eventData?: any
  ) => {
    try {
      const response = await fetch("/api/admin/notifications/rules", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test",
          ruleId,
          eventType: eventType || "order.created",
          eventData: eventData || {
            orderId: "test-order-123",
            orderNumber: "ORD-2024-001",
            customerName: "Test Customer",
            totalAmount: 1000000,
          },
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message);
        setTestDialogOpen(false);
      } else {
        const error = await response.json();
        toast.error(error.error || "Test thất bại");
      }
    } catch (error) {
      console.error("Error testing rule:", error);
      toast.error("Không thể test rule");
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ERROR":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "WARNING":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "SYSTEM":
        return <Settings className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      LOW: "secondary",
      NORMAL: "outline",
      HIGH: "default",
      URGENT: "destructive",
    };
    return (
      <Badge variant={variants[priority as keyof typeof variants] as any}>
        {priority}
      </Badge>
    );
  };

  const getEventTypeLabel = (eventType: string) => {
    const labels: Record<string, string> = {
      "product.stock.low": "Hàng sắp hết",
      "product.stock.empty": "Hết hàng",
      "order.created": "Đơn hàng mới",
      "order.status.changed": "Thay đổi trạng thái đơn hàng",
      "payment.failed": "Thanh toán thất bại",
      "user.registered": "Đăng ký người dùng mới",
      "system.error": "Lỗi hệ thống",
    };
    return labels[eventType] || eventType;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Notification Rules
          </h2>
          <p className="text-muted-foreground">
            Quản lý rules tự động tạo thông báo cho các sự kiện hệ thống
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={loadRules}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          <Dialog open={testDialogOpen} onOpenChange={setTestDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Play className="h-4 w-4 mr-2" />
                Test Rules
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Test Notification Rules</DialogTitle>
                <DialogDescription>
                  Test các rules với dữ liệu mẫu
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Event Type</Label>
                  <Select defaultValue="order.created">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {eventTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {getEventTypeLabel(type)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setTestDialogOpen(false)}
                  >
                    Hủy
                  </Button>
                  <Button onClick={() => testRule()}>
                    <Play className="h-4 w-4 mr-2" />
                    Chạy Test
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Tạo Rule
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{rules.length}</div>
            <p className="text-sm text-muted-foreground">Tổng số rules</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {rules.filter((r) => r.isActive).length}
            </div>
            <p className="text-sm text-muted-foreground">
              Rules đang hoạt động
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {rules.filter((r) => !r.isActive).length}
            </div>
            <p className="text-sm text-muted-foreground">Rules tạm dừng</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{eventTypes.length}</div>
            <p className="text-sm text-muted-foreground">Loại sự kiện</p>
          </CardContent>
        </Card>
      </div>

      {/* Rules Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Rules</CardTitle>
          <CardDescription>
            Quản lý và cấu hình các rules notification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Rule</TableHead>
                <TableHead>Event Type</TableHead>
                <TableHead>Notification Type</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead className="text-right">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{rule.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {rule.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getEventTypeLabel(rule.eventType)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(rule.notificationTemplate.type)}
                      <span className="text-sm">
                        {rule.notificationTemplate.type}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getPriorityBadge(rule.notificationTemplate.priority)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {rule.notificationTemplate.targetType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={(checked) => {
                        // Update rule status
                        console.log(`Toggle rule ${rule.id}: ${checked}`);
                      }}
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => testRule(rule.id)}
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedRule(rule);
                          setCreateDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create/Edit Rule Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedRule ? "Chỉnh sửa Rule" : "Tạo Rule mới"}
            </DialogTitle>
            <DialogDescription>
              Cấu hình rule để tự động tạo thông báo cho các sự kiện
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Tên Rule</Label>
                <Input placeholder="Nhập tên rule..." />
              </div>
              <div className="space-y-2">
                <Label>Event Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn event type" />
                  </SelectTrigger>
                  <SelectContent>
                    {eventTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {getEventTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Mô tả</Label>
              <Textarea placeholder="Mô tả rule..." />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Notification Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="INFO">Info</SelectItem>
                    <SelectItem value="SUCCESS">Success</SelectItem>
                    <SelectItem value="WARNING">Warning</SelectItem>
                    <SelectItem value="ERROR">Error</SelectItem>
                    <SelectItem value="SYSTEM">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Priority</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="NORMAL">Normal</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="URGENT">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Tiêu đề thông báo</Label>
              <Input placeholder="Nhập tiêu đề..." />
            </div>

            <div className="space-y-2">
              <Label>Nội dung thông báo</Label>
              <Textarea placeholder="Nhập nội dung thông báo..." />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setCreateDialogOpen(false);
                  setSelectedRule(null);
                }}
              >
                Hủy
              </Button>
              <Button>{selectedRule ? "Cập nhật" : "Tạo Rule"}</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
