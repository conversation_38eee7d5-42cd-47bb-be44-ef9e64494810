import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for stock movement
const stockMovementSchema = z.object({
  inventoryEntryId: z.string().min(1, "ID kho hàng là bắt buộc"),
  type: z.enum(["IN", "OUT", "TRANSFER", "ADJUSTMENT"], {
    errorMap: () => ({ message: "Loại giao dịch không hợp lệ" }),
  }),
  quantity: z.number().min(1, "Số lượng phải > 0"),
  reason: z.string().optional(),
  reference: z.string().optional(),
  notes: z.string().optional(),
  createdBy: z.string().optional(),
});

// GET /api/admin/inventory/movements - Get stock movements with pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const type = searchParams.get("type") || "";
    const productId = searchParams.get("productId") || "";
    const inventoryEntryId = searchParams.get("inventoryEntryId") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (type) {
      where.type = type;
    }

    if (inventoryEntryId) {
      where.inventoryEntryId = inventoryEntryId;
    }

    if (productId) {
      where.inventoryEntry = {
        productId,
      };
    }

    // Get total count
    const total = await prisma.stockMovement.count({ where });

    // Get stock movements with pagination
    const movements = await prisma.stockMovement.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        inventoryEntry: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                images: true,
              },
            },
          },
        },
      },
    });

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: movements,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Get stock movements error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy lịch sử giao dịch kho" },
      { status: 500 }
    );
  }
}

// POST /api/admin/inventory/movements - Create stock movement
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = stockMovementSchema.parse(body);

    // Check if inventory entry exists
    const inventoryEntry = await prisma.inventoryEntry.findUnique({
      where: { id: validatedData.inventoryEntryId },
    });

    if (!inventoryEntry) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thông tin kho hàng" },
        { status: 404 }
      );
    }

    // Calculate new quantities based on movement type
    let newQuantity = inventoryEntry.quantity;
    let newAvailable = inventoryEntry.available;

    if (validatedData.type === "IN") {
      newQuantity += validatedData.quantity;
      newAvailable += validatedData.quantity;
    } else if (validatedData.type === "OUT") {
      if (validatedData.quantity > inventoryEntry.available) {
        return NextResponse.json(
          { success: false, error: "Số lượng xuất kho vượt quá số lượng có sẵn" },
          { status: 400 }
        );
      }
      newQuantity -= validatedData.quantity;
      newAvailable -= validatedData.quantity;
    } else if (validatedData.type === "ADJUSTMENT") {
      // For adjustment, quantity can be positive or negative
      const adjustmentAmount = validatedData.quantity;
      newQuantity = Math.max(0, inventoryEntry.quantity + adjustmentAmount);
      newAvailable = Math.max(0, inventoryEntry.available + adjustmentAmount);
    }

    // Create stock movement and update inventory in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create stock movement
      const movement = await tx.stockMovement.create({
        data: validatedData,
        include: {
          inventoryEntry: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  images: true,
                },
              },
            },
          },
        },
      });

      // Update inventory entry
      await tx.inventoryEntry.update({
        where: { id: validatedData.inventoryEntryId },
        data: {
          quantity: newQuantity,
          available: newAvailable,
        },
      });

      return movement;
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: "Giao dịch kho đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create stock movement error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo giao dịch kho" },
      { status: 500 }
    );
  }
}
