/**
 * <PERSON><PERSON> (Mock Service Worker) Handlers
 * Đ<PERSON>nh nghĩa các mock handlers cho API testing
 */

import { http, HttpResponse } from "msw";

// Mock data
const mockProducts = [
  {
    id: "product_1",
    name: "Áo thun nam basic",
    slug: "ao-thun-nam-basic",
    description: "Áo thun cotton 100% cao cấp, form regular fit",
    price: 299000,
    salePrice: 199000,
    stock: 50,
    images: [
      "https://example.com/images/ao-thun-1.jpg",
      "https://example.com/images/ao-thun-2.jpg",
    ],
    categoryId: "cat_1",
    featured: true,
    status: "ACTIVE",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    category: {
      id: "cat_1",
      name: "<PERSON>o thun",
      slug: "ao-thun",
    },
  },
  {
    id: "product_2",
    name: "Quần jeans nữ skinny",
    slug: "quan-jeans-nu-skinny",
    description: "Quần jeans nữ form skinny, chất liệu denim cao cấp",
    price: 599000,
    salePrice: null,
    stock: 30,
    images: ["https://example.com/images/quan-jeans-1.jpg"],
    categoryId: "cat_2",
    featured: false,
    status: "ACTIVE",
    createdAt: "2024-01-02T00:00:00.000Z",
    updatedAt: "2024-01-02T00:00:00.000Z",
    category: {
      id: "cat_2",
      name: "Quần jeans",
      slug: "quan-jeans",
    },
  },
  {
    id: "product_3",
    name: "Váy maxi hoa",
    slug: "vay-maxi-hoa",
    description: "Váy maxi họa tiết hoa, chất liệu voan mềm mại",
    price: 799000,
    salePrice: 599000,
    stock: 20,
    images: ["https://example.com/images/vay-maxi-1.jpg"],
    categoryId: "cat_3",
    featured: true,
    status: "ACTIVE",
    createdAt: "2024-01-03T00:00:00.000Z",
    updatedAt: "2024-01-03T00:00:00.000Z",
    category: {
      id: "cat_3",
      name: "Váy",
      slug: "vay",
    },
  },
];

const mockCategories = [
  {
    id: "cat_1",
    name: "Áo thun",
    slug: "ao-thun",
    description: "Các loại áo thun nam nữ",
    image: "https://example.com/images/cat-ao-thun.jpg",
    status: "ACTIVE",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    id: "cat_2",
    name: "Quần jeans",
    slug: "quan-jeans",
    description: "Quần jeans nam nữ các kiểu dáng",
    image: "https://example.com/images/cat-quan-jeans.jpg",
    status: "ACTIVE",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    id: "cat_3",
    name: "Váy",
    slug: "vay",
    description: "Váy nữ các loại",
    image: "https://example.com/images/cat-vay.jpg",
    status: "ACTIVE",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
];

const mockUsers = [
  {
    id: "user_1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "USER",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    id: "user_2",
    name: "Admin User",
    email: "<EMAIL>",
    role: "ADMIN",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
];

const mockOrders = [
  {
    id: "order_1",
    userId: "user_1",
    status: "PENDING",
    total: 398000,
    items: [
      {
        id: "item_1",
        productId: "product_1",
        quantity: 2,
        price: 199000,
        product: mockProducts[0],
      },
    ],
    shippingAddress: {
      fullName: "John Doe",
      phone: "0123456789",
      address: "123 Main St",
      city: "Ho Chi Minh City",
      district: "District 1",
      ward: "Ward 1",
    },
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
];

export const handlers = [
  // Products API
  http.get("/api/products", ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search");
    const category = url.searchParams.get("category");
    const minPrice = url.searchParams.get("minPrice");
    const maxPrice = url.searchParams.get("maxPrice");
    const sort = url.searchParams.get("sort");

    let filteredProducts = [...mockProducts];

    // Apply search filter
    if (search) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.name.toLowerCase().includes(search.toLowerCase()) ||
          product.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply category filter
    if (category) {
      filteredProducts = filteredProducts.filter(
        (product) => product.category.slug === category
      );
    }

    // Apply price filter
    if (minPrice || maxPrice) {
      filteredProducts = filteredProducts.filter((product) => {
        const price = product.salePrice || product.price;
        const min = minPrice ? parseInt(minPrice) : 0;
        const max = maxPrice ? parseInt(maxPrice) : Infinity;
        return price >= min && price <= max;
      });
    }

    // Apply sorting
    if (sort) {
      switch (sort) {
        case "price_asc":
          filteredProducts.sort((a, b) => (a.salePrice || a.price) - (b.salePrice || b.price));
          break;
        case "price_desc":
          filteredProducts.sort((a, b) => (b.salePrice || b.price) - (a.salePrice || a.price));
          break;
        case "name_asc":
          filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case "name_desc":
          filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
          break;
        case "newest":
          filteredProducts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          break;
        case "oldest":
          filteredProducts.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
          break;
      }
    }

    // Apply pagination
    const total = filteredProducts.length;
    const totalPages = Math.ceil(total / limit);
    const skip = (page - 1) * limit;
    const paginatedProducts = filteredProducts.slice(skip, skip + limit);

    return HttpResponse.json({
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  }),

  http.get("/api/products/:slug", ({ params }) => {
    const { slug } = params;
    const product = mockProducts.find((p) => p.slug === slug);

    if (!product) {
      return HttpResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    return HttpResponse.json({ product });
  }),

  // Categories API
  http.get("/api/categories", () => {
    return HttpResponse.json({ categories: mockCategories });
  }),

  http.get("/api/categories/:slug", ({ params }) => {
    const { slug } = params;
    const category = mockCategories.find((c) => c.slug === slug);

    if (!category) {
      return HttpResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    return HttpResponse.json({ category });
  }),

  // Auth API
  http.post("/api/auth/register", async ({ request }) => {
    const body = await request.json() as any;
    const { name, email, password } = body;

    // Validate required fields
    if (!name || !email || !password) {
      return HttpResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = mockUsers.find((u) => u.email === email);
    if (existingUser) {
      return HttpResponse.json(
        { error: "User already exists" },
        { status: 400 }
      );
    }

    // Create new user
    const newUser = {
      id: `user_${Date.now()}`,
      name,
      email,
      role: "USER",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockUsers.push(newUser);

    return HttpResponse.json(
      {
        message: "User created successfully",
        user: {
          id: newUser.id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
        },
      },
      { status: 201 }
    );
  }),

  // Cart API
  http.get("/api/cart", () => {
    return HttpResponse.json({
      items: [
        {
          id: "cart_item_1",
          productId: "product_1",
          quantity: 2,
          product: mockProducts[0],
        },
      ],
    });
  }),

  http.post("/api/cart", async ({ request }) => {
    const body = await request.json() as any;
    const { productId, quantity } = body;

    const product = mockProducts.find((p) => p.id === productId);
    if (!product) {
      return HttpResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    return HttpResponse.json(
      {
        message: "Item added to cart",
        item: {
          id: `cart_item_${Date.now()}`,
          productId,
          quantity,
          product,
        },
      },
      { status: 201 }
    );
  }),

  // Orders API
  http.get("/api/orders", () => {
    return HttpResponse.json({ orders: mockOrders });
  }),

  http.post("/api/orders", async ({ request }) => {
    const body = await request.json() as any;

    const newOrder = {
      id: `order_${Date.now()}`,
      ...body,
      status: "PENDING",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockOrders.push(newOrder);

    return HttpResponse.json(
      {
        message: "Order created successfully",
        order: newOrder,
      },
      { status: 201 }
    );
  }),

  // Error simulation handlers
  http.get("/api/products/error", () => {
    return HttpResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }),

  http.post("/api/auth/register/error", () => {
    return HttpResponse.json(
      { error: "Database connection failed" },
      { status: 500 }
    );
  }),

  // Slow response simulation
  http.get("/api/products/slow", async () => {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    return HttpResponse.json({ products: mockProducts.slice(0, 1) });
  }),
];
