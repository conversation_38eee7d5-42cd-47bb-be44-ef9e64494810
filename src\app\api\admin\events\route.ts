import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAdminToken } from "@/lib/admin-auth";
import { z } from "zod";

const createEventSchema = z.object({
  title: z.string().min(1, "Tiêu đề không được để trống"),
  description: z.string().optional(),
  content: z.string().optional(),
  slug: z.string().min(1, "Slug không được để trống"),
  type: z.enum([
    "PROMOTION",
    "LAUNCH",
    "WORKSHOP",
    "WEBINAR",
    "SALE",
    "SEASONAL",
    "COMMUNITY",
    "OTHER",
  ]),
  status: z
    .enum([
      "DRAFT",
      "PUBLISHED",
      "SCHEDULED",
      "ONGOING",
      "COMPLETED",
      "CANCELLED",
    ])
    .default("DRAFT"),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  isAllDay: z.boolean().default(false),
  location: z.string().optional(),
  maxAttendees: z.number().positive().optional(),
  price: z.number().min(0).optional(),
  currency: z.string().default("VND"),
  image: z.string().optional(),
  tags: z.array(z.string()).default([]),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
});

const updateEventSchema = createEventSchema.partial().omit({ slug: true });

// GET /api/admin/events - List all events
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") || "";
    const status = searchParams.get("status") || "";
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    }

    if (startDate) {
      where.startDate = {
        gte: new Date(startDate),
      };
    }

    if (endDate) {
      where.startDate = {
        ...where.startDate,
        lte: new Date(endDate),
      };
    }

    // Get events with creator info
    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { startDate: "desc" },
        skip,
        take: limit,
      }),
      prisma.event.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách sự kiện" },
      { status: 500 }
    );
  }
}

// POST /api/admin/events - Create new event
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createEventSchema.parse(body);

    // Check if slug already exists
    const existingEvent = await prisma.event.findUnique({
      where: { slug: validatedData.slug },
    });

    if (existingEvent) {
      return NextResponse.json(
        { success: false, error: "Slug này đã được sử dụng" },
        { status: 400 }
      );
    }

    // Validate date logic
    if (
      validatedData.endDate &&
      validatedData.endDate <= validatedData.startDate
    ) {
      return NextResponse.json(
        { success: false, error: "Ngày kết thúc phải sau ngày bắt đầu" },
        { status: 400 }
      );
    }

    const event = await prisma.event.create({
      data: {
        ...validatedData,
        createdBy: adminToken.id as string,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: event,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating event:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo sự kiện" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/events - Bulk update events
export async function PUT(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { ids, data } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const validatedData = updateEventSchema.parse(data);

    await prisma.event.updateMany({
      where: {
        id: { in: ids },
      },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      message: `Đã cập nhật ${ids.length} sự kiện`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error bulk updating events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật sự kiện" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/events - Bulk delete events
export async function DELETE(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get("ids");

    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const ids = idsParam.split(",");

    await prisma.event.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    return NextResponse.json({
      success: true,
      message: `Đã xóa ${ids.length} sự kiện`,
    });
  } catch (error) {
    console.error("Error bulk deleting events:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa sự kiện" },
      { status: 500 }
    );
  }
}
