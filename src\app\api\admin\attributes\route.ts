import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schemas
const attributeSchema = z.object({
  name: z.string().min(1, "Tên thuộc tính là bắt buộc"),
  slug: z.string().optional(),
  description: z.string().optional(),
  type: z.enum([
    "TEXT",
    "NUMBER",
    "COLOR",
    "SIZE",
    "BOOLEAN",
    "SELECT",
    "MULTI_SELECT",
  ]),
  isRequired: z.boolean().default(false),
  isFilterable: z.boolean().default(true),
  sortOrder: z.number().default(0),
  values: z
    .array(
      z.object({
        value: z.string().min(1),
        slug: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attributes - <PERSON><PERSON><PERSON> s<PERSON>ch attributes
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") || "";
    const isRequired = searchParams.get("isRequired");
    const isFilterable = searchParams.get("isFilterable");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const sortBy = searchParams.get("sortBy") || "sortOrder";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (isRequired !== null) {
      where.isRequired = isRequired === "true";
    }

    if (isFilterable !== null) {
      where.isFilterable = isFilterable === "true";
    }

    // Get total count
    const total = await prisma.attribute.count({ where });

    // Get attributes
    const attributes = await prisma.attribute.findMany({
      where,
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            values: true,
            products: true,
          },
        },
      },
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return NextResponse.json({
      success: true,
      data: attributes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get attributes error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách thuộc tính" },
      { status: 500 }
    );
  }
}

// POST /api/admin/attributes - Tạo attribute mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = attributeSchema.parse(body);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = validatedData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check if slug already exists
    const existingAttribute = await prisma.attribute.findUnique({
      where: { slug: validatedData.slug },
    });

    if (existingAttribute) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // Create attribute with values in transaction
    const attribute = await prisma.$transaction(async (tx) => {
      const newAttribute = await tx.attribute.create({
        data: {
          name: validatedData.name,
          slug: validatedData.slug!,
          description: validatedData.description,
          type: validatedData.type,
          isRequired: validatedData.isRequired,
          isFilterable: validatedData.isFilterable,
          sortOrder: validatedData.sortOrder,
        },
      });

      // Create attribute values if provided
      if (validatedData.values && validatedData.values.length > 0) {
        for (const valueData of validatedData.values) {
          const valueSlug =
            valueData.slug ||
            valueData.value
              .toLowerCase()
              .replace(/\s+/g, "-")
              .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
              .replace(/[èéẹẻẽêềếệểễ]/g, "e")
              .replace(/[ìíịỉĩ]/g, "i")
              .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
              .replace(/[ùúụủũưừứựửữ]/g, "u")
              .replace(/[ỳýỵỷỹ]/g, "y")
              .replace(/[đ]/g, "d")
              .replace(/[^a-z0-9-]/g, "");

          await tx.attributeValue.create({
            data: {
              attributeId: newAttribute.id,
              value: valueData.value,
              slug: valueSlug,
              sortOrder: valueData.sortOrder,
            },
          });
        }
      }

      return newAttribute;
    });

    // Fetch the created attribute with values
    const createdAttribute = await prisma.attribute.findUnique({
      where: { id: attribute.id },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            values: true,
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: createdAttribute,
      message: "Thuộc tính đã được tạo thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo thuộc tính" },
      { status: 500 }
    );
  }
}
