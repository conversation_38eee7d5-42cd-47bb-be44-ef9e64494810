# Hướng dẫn sử dụng thư viện Admin đã chuẩn hóa

## Tổng quan

Thư viện admin đã được chuẩn hóa để cung cấp các components và utilities tái sử dụng cho việc xây dựng admin dashboard. Thư viện bao gồm:

- **API utilities**: Middleware, controllers, error handling, pagination
- **Frontend hooks**: Data fetching, form handling, table management
- **Components**: Data table, forms, loading states
- **Types**: TypeScript interfaces cho tất cả admin operations

## Cấu trúc thư viện

```
src/lib/admin/
├── api/                    # API utilities
│   ├── middleware.ts       # Admin auth middleware
│   ├── base-controller.ts  # Base CRUD controller
│   ├── responses.ts        # Response formatters
│   ├── pagination.ts       # Pagination utilities
│   └── errors.ts           # Error handling
├── hooks/                  # React hooks
│   ├── use-admin-data.ts   # Data fetching hooks
│   ├── use-admin-form.ts   # Form handling hooks
│   └── use-admin-table.ts  # Table management hooks
├── components/             # React components
│   ├── AdminDataTable.tsx  # Reusable data table
│   ├── AdminForm.tsx       # Reusable form wrapper
│   └── AdminLoadingState.tsx # Loading/error states
├── controllers/            # Specific controllers
│   ├── ProductController.ts
│   └── CategoryController.ts
└── types/                  # TypeScript types
    ├── api.ts
    ├── forms.ts
    └── tables.ts
```

## 1. Tạo API Controller mới

### Bước 1: Tạo Controller

```typescript
// src/lib/admin/controllers/YourController.ts
import { prisma } from '@/lib/prisma';
import { BaseAdminController } from '../api/base-controller';
import { ErrorHelper } from '../api/errors';

export class YourController extends BaseAdminController<YourFormData> {
  constructor() {
    super(
      prisma.yourModel,
      'tên model',
      ['field1', 'field2'], // searchable fields
      {
        // include relations
        relatedModel: {
          select: {
            id: true,
            name: true,
          },
        },
      }
    );
  }

  // Override validation methods
  protected async validateCreateData(data: Partial<YourFormData>): Promise<void> {
    if (!data.requiredField) {
      throw ErrorHelper.validation('Thiếu thông tin bắt buộc');
    }
  }

  // Override transform methods
  protected async transformCreateData(data: Partial<YourFormData>): Promise<any> {
    return {
      ...data,
      // Transform data before saving
    };
  }
}
```

### Bước 2: Tạo API Routes

```typescript
// src/app/api/admin/your-model/route.ts
import { NextRequest } from "next/server";
import { withAdminAuth } from "@/lib/admin/api/middleware";
import { YourController } from "@/lib/admin/controllers/YourController";

const controller = new YourController();

export async function GET(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await controller.handleList(request);
  });
}

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await controller.handleCreate(request);
  });
}
```

```typescript
// src/app/api/admin/your-model/[id]/route.ts
import { NextRequest } from "next/server";
import { withAdminAuth } from "@/lib/admin/api/middleware";
import { YourController } from "@/lib/admin/controllers/YourController";

const controller = new YourController();

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  return await controller.handleGet(request, { params });
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  return withAdminAuth(request, async () => {
    return await controller.handleUpdate(request, { params });
  });
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  return withAdminAuth(request, async () => {
    return await controller.handleDelete(request, { params });
  });
}
```

## 2. Tạo Admin Page với Data Table

```typescript
'use client';

import React, { useState } from 'react';
import { AdminDataTable } from '@/lib/admin/components';
import { useAdminData, useAdminCrud } from '@/lib/admin/hooks';
import { TableConfig, TableColumn } from '@/lib/admin/types';

export default function YourAdminPage() {
  const [selectedItems, setSelectedItems] = useState([]);

  // Data fetching
  const {
    data,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  } = useAdminData({
    endpoint: '/api/admin/your-model',
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const { remove, bulkDelete } = useAdminCrud('/api/admin/your-model');

  // Table configuration
  const columns: TableColumn[] = [
    {
      key: 'name',
      title: 'Tên',
      dataIndex: 'name',
      sortable: true,
      searchable: true,
    },
    // More columns...
  ];

  const tableConfig: TableConfig = {
    columns,
    rowKey: 'id',
    pagination: { enabled: true, pageSize: params.limit },
    selection: { enabled: true, type: 'checkbox' },
    search: { enabled: true, placeholder: 'Tìm kiếm...' },
    actions: {
      enabled: true,
      items: [
        {
          key: 'edit',
          label: 'Sửa',
          onClick: (record) => {
            // Handle edit
          },
        },
        {
          key: 'delete',
          label: 'Xóa',
          type: 'danger',
          onClick: async (record) => {
            if (confirm('Bạn có chắc chắn muốn xóa?')) {
              await remove(record.id);
              refresh();
            }
          },
        },
      ],
    },
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Your Admin Page</h1>
      
      <AdminDataTable
        config={tableConfig}
        dataSource={data}
        loading={loading}
        onRefresh={refresh}
        onSearch={(search) => setParams({ search })}
        onPageChange={(page) => setParams({ page })}
        selectedRows={selectedItems}
        onSelectionChange={setSelectedItems}
      />
    </div>
  );
}
```

## 3. Tạo Form với AdminForm

```typescript
'use client';

import React from 'react';
import { AdminForm } from '@/lib/admin/components';
import { FormConfig, FormField } from '@/lib/admin/types';
import { useAdminCrud } from '@/lib/admin/hooks';

export default function YourFormPage() {
  const { create, loading } = useAdminCrud('/api/admin/your-model');

  const fields: FormField[] = [
    {
      name: 'name',
      label: 'Tên',
      type: 'text',
      required: true,
      placeholder: 'Nhập tên...',
      validation: {
        required: true,
        min: 2,
        max: 100,
      },
    },
    {
      name: 'description',
      label: 'Mô tả',
      type: 'textarea',
      placeholder: 'Nhập mô tả...',
    },
    {
      name: 'status',
      label: 'Trạng thái',
      type: 'select',
      options: [
        { value: 'ACTIVE', label: 'Hoạt động' },
        { value: 'INACTIVE', label: 'Không hoạt động' },
      ],
      defaultValue: 'ACTIVE',
    },
  ];

  const formConfig: FormConfig = {
    fields,
    layout: 'vertical',
    submitText: 'Tạo mới',
    cancelText: 'Hủy',
    loading,
    onSubmit: async (data) => {
      const result = await create(data);
      if (result) {
        // Handle success
        window.location.href = '/admin/your-model';
      }
    },
    onCancel: () => {
      window.history.back();
    },
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <h1 className="text-3xl font-bold">Tạo mới</h1>
      
      <AdminForm config={formConfig} />
    </div>
  );
}
```

## 4. Sử dụng Loading và Error States

```typescript
import { 
  AdminLoadingState, 
  AdminErrorState, 
  AdminEmptyState 
} from '@/lib/admin/components';

// Loading state
if (loading) {
  return <AdminLoadingState type="table" message="Đang tải dữ liệu..." />;
}

// Error state
if (error) {
  return (
    <AdminErrorState
      title="Lỗi tải dữ liệu"
      description="Không thể tải danh sách"
      error={error}
      onRetry={refresh}
    />
  );
}

// Empty state
if (data.length === 0) {
  return (
    <AdminEmptyState
      title="Chưa có dữ liệu"
      description="Bắt đầu bằng cách tạo mục đầu tiên"
      action={{
        label: 'Tạo mới',
        onClick: () => window.location.href = '/admin/your-model/create',
      }}
    />
  );
}
```

## 5. Best Practices

### API Controllers
- Luôn validate dữ liệu trong `validateCreateData` và `validateUpdateData`
- Sử dụng `ErrorHelper` để throw errors với messages phù hợp
- Override `transformCreateData` và `transformUpdateData` để xử lý dữ liệu
- Implement custom methods cho business logic specific

### Frontend Components
- Sử dụng `useAdminData` cho data fetching với pagination và search
- Sử dụng `useAdminCrud` cho CRUD operations
- Configure table columns với proper types và render functions
- Handle loading và error states properly

### Error Handling
- Sử dụng `withAdminAuth` middleware cho tất cả admin routes
- Catch và handle errors properly trong controllers
- Provide meaningful error messages cho users

### Performance
- Sử dụng pagination cho large datasets
- Implement proper indexing trong database
- Cache data khi cần thiết
- Optimize queries với proper includes

## 6. Migration từ code cũ

1. **API Routes**: Thay thế manual auth checking bằng `withAdminAuth`
2. **Controllers**: Extend `BaseAdminController` thay vì viết CRUD từ đầu
3. **Frontend**: Sử dụng `AdminDataTable` thay vì custom tables
4. **Forms**: Sử dụng `AdminForm` với configuration-based approach
5. **Error Handling**: Sử dụng standardized error responses

Thư viện này giúp giảm code duplication, tăng consistency, và dễ dàng maintain admin functionality.
