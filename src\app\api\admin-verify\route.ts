import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";

export async function GET(request: NextRequest) {
  try {
    // Lấy admin session token từ cookies
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return NextResponse.json(
        { error: "No admin session found" },
        { status: 401 }
      );
    }

    // Verify JWT token using jose (same as admin-login)
    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    try {
      const { payload } = await jwtVerify(adminToken, secret);

      // Kiểm tra xem token có role admin hoặc moderator không
      if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
        return NextResponse.json(
          { error: "Invalid admin role" },
          { status: 403 }
        );
      }

      // Tr<PERSON> về thông tin admin nếu valid
      return NextResponse.json({
        success: true,
        admin: {
          id: payload.id,
          email: payload.email,
          role: payload.role,
        },
      });
    } catch (verifyError) {
      console.error("JWT verification failed:", verifyError);
      return NextResponse.json(
        { error: "Invalid admin session" },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Admin verify error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
