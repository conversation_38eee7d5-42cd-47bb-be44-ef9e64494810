import { Page, expect } from "@playwright/test";

export interface TestProduct {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  stock: number;
  sku: string;
  category?: string;
}

export interface TestCategory {
  name: string;
  description: string;
  slug: string;
  parentId?: string;
}

export interface TestPost {
  title: string;
  summary: string;
  content: string;
  slug: string;
  status: "DRAFT" | "PUBLISHED";
}

export const TEST_PRODUCTS: TestProduct[] = [
  {
    name: "Áo thun basic nam",
    description: "Áo thun nam cotton thoáng mát",
    price: 299000,
    salePrice: 199000,
    stock: 100,
    sku: "SHIRT-MEN-001",
    category: "Thời trang nam",
  },
  {
    name: "Quần jeans nữ",
    description: "Quần jeans nữ ôm form đẹp",
    price: 599000,
    salePrice: 449000,
    stock: 50,
    sku: "JEANS-WOMEN-001",
    category: "Thời trang nữ",
  },
  {
    name: "Giày sneaker unisex",
    description: "<PERSON><PERSON><PERSON>y sneaker thời trang cho cả nam và nữ",
    price: 899000,
    stock: 30,
    sku: "SHOES-UNISEX-001",
    category: "Giày dép",
  },
];

export const TEST_CATEGORIES: TestCategory[] = [
  {
    name: "Thời trang nam",
    description: "Các sản phẩm thời trang dành cho nam giới",
    slug: "thoi-trang-nam",
  },
  {
    name: "Thời trang nữ",
    description: "Các sản phẩm thời trang dành cho nữ giới",
    slug: "thoi-trang-nu",
  },
  {
    name: "Giày dép",
    description: "Các loại giày và dép",
    slug: "giay-dep",
  },
  {
    name: "Phụ kiện",
    description: "Các phụ kiện thời trang",
    slug: "phu-kien",
  },
];

export const TEST_POSTS: TestPost[] = [
  {
    title: "Hướng dẫn chọn size áo thun",
    summary: "Cách chọn size áo thun phù hợp với từng dáng người",
    content: "Nội dung chi tiết về cách chọn size áo thun phù hợp...",
    slug: "huong-dan-chon-size-ao-thun",
    status: "PUBLISHED",
  },
  {
    title: "Xu hướng thời trang 2024",
    summary: "Tổng hợp các xu hướng thời trang hot nhất năm 2024",
    content: "Năm 2024 đánh dấu sự trở lại của nhiều phong cách thời trang...",
    slug: "xu-huong-thoi-trang-2024",
    status: "PUBLISHED",
  },
  {
    title: "Bảo quản giày sneaker đúng cách",
    summary: "Mẹo bảo quản giày sneaker để giữ form và màu sắc",
    content: "Việc bảo quản giày sneaker đúng cách sẽ giúp đôi giày của bạn...",
    slug: "bao-quan-giay-sneaker-dung-cach",
    status: "DRAFT",
  },
];

/**
 * Helper để tạo sản phẩm test
 */
export async function createTestProduct(
  page: Page,
  product: TestProduct
): Promise<void> {
  await page.goto("/admin/products");
  await page.getByRole("button", { name: "Thêm sản phẩm" }).click();

  await page.getByLabel("Tên sản phẩm").fill(product.name);
  await page.getByLabel("Mô tả ngắn").fill(product.description);
  await page.getByLabel("Giá").fill(product.price.toString());

  if (product.salePrice) {
    await page.getByLabel("Giá khuyến mãi").fill(product.salePrice.toString());
  }

  await page.getByLabel("Số lượng tồn kho").fill(product.stock.toString());
  await page.getByLabel("SKU").fill(product.sku);

  if (product.category) {
    const categorySelect = page.getByLabel("Danh mục");
    if (await categorySelect.isVisible()) {
      await categorySelect.selectOption(product.category);
    }
  }

  await page.getByRole("button", { name: "Tạo sản phẩm" }).click();
  await expect(page.getByText("Sản phẩm đã được tạo thành công")).toBeVisible();
}

/**
 * Helper để tạo danh mục test
 */
export async function createTestCategory(
  page: Page,
  category: TestCategory
): Promise<void> {
  await page.goto("/admin/categories");
  await page.getByRole("button", { name: "Thêm danh mục" }).click();

  await page.getByLabel("Tên danh mục").fill(category.name);
  await page.getByLabel("Mô tả").fill(category.description);
  await page.getByLabel("Slug").fill(category.slug);

  if (category.parentId) {
    await page.getByLabel("Danh mục cha").selectOption(category.parentId);
  }

  await page.getByRole("button", { name: "Tạo danh mục" }).click();
  await expect(page.getByText("Danh mục đã được tạo thành công")).toBeVisible();
}

/**
 * Helper để tạo bài viết test
 */
export async function createTestPost(
  page: Page,
  post: TestPost
): Promise<void> {
  await page.goto("/admin/posts");
  await page.getByRole("button", { name: "Thêm bài viết" }).click();

  await page.getByLabel("Tiêu đề").fill(post.title);
  await page.getByLabel("Tóm tắt").fill(post.summary);
  await page.getByLabel("Nội dung").fill(post.content);
  await page.getByLabel("Slug").fill(post.slug);
  await page.getByLabel("Trạng thái").selectOption(post.status);

  await page.getByRole("button", { name: "Tạo bài viết" }).click();
  await expect(page.getByText("Bài viết đã được tạo thành công")).toBeVisible();
}

/**
 * Helper để xóa tất cả dữ liệu test
 */
export async function cleanupTestData(page: Page): Promise<void> {
  // Xóa sản phẩm test
  await page.goto("/admin/products");
  const productRows = page.locator('[data-testid="product-row"]');
  const productCount = await productRows.count();

  for (let i = 0; i < productCount; i++) {
    const row = productRows.nth(i);
    const productName = await row.getByTestId("product-name").textContent();

    if (
      (productName && productName.includes("Test")) ||
      (productName && productName.includes("Playwright"))
    ) {
      await row.getByRole("button", { name: "Xóa" }).click();
      await page.getByRole("button", { name: "Xác nhận xóa" }).click();
      await expect(
        page.getByText("Sản phẩm đã được xóa thành công")
      ).toBeVisible();
    }
  }

  // Xóa danh mục test
  await page.goto("/admin/categories");
  const categoryRows = page.locator('[data-testid="category-row"]');
  const categoryCount = await categoryRows.count();

  for (let i = 0; i < categoryCount; i++) {
    const row = categoryRows.nth(i);
    const categoryName = await row.getByTestId("category-name").textContent();

    if (
      (categoryName && categoryName.includes("Test")) ||
      (categoryName && categoryName.includes("Playwright"))
    ) {
      await row.getByRole("button", { name: "Xóa" }).click();
      await page.getByRole("button", { name: "Xác nhận xóa" }).click();
      await expect(
        page.getByText("Danh mục đã được xóa thành công")
      ).toBeVisible();
    }
  }

  // Xóa bài viết test
  await page.goto("/admin/posts");
  const postRows = page.locator('[data-testid="post-row"]');
  const postCount = await postRows.count();

  for (let i = 0; i < postCount; i++) {
    const row = postRows.nth(i);
    const postTitle = await row.getByTestId("post-title").textContent();

    if (
      (postTitle && postTitle.includes("Test")) ||
      (postTitle && postTitle.includes("Playwright"))
    ) {
      await row.getByRole("button", { name: "Xóa" }).click();
      await page.getByRole("button", { name: "Xác nhận xóa" }).click();
      await expect(
        page.getByText("Bài viết đã được xóa thành công")
      ).toBeVisible();
    }
  }
}

/**
 * Helper để wait cho element visible với timeout
 */
export async function waitForElement(
  page: Page,
  selector: string,
  timeout = 10000
): Promise<void> {
  await page.waitForSelector(selector, { state: "visible", timeout });
}

/**
 * Helper để scroll đến element
 */
export async function scrollToElement(
  page: Page,
  selector: string
): Promise<void> {
  await page.locator(selector).scrollIntoViewIfNeeded();
}

/**
 * Helper để lấy text content của element
 */
export async function getElementText(
  page: Page,
  selector: string
): Promise<string | null> {
  return await page.locator(selector).textContent();
}

/**
 * Helper để check xem element có tồn tại không
 */
export async function elementExists(
  page: Page,
  selector: string
): Promise<boolean> {
  return (await page.locator(selector).count()) > 0;
}
