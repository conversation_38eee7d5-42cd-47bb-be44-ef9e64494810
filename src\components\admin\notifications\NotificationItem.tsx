"use client";

import { useState } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Settings,
  ExternalLink,
  Check,
  Trash2,
  MoreHorizontal,
  Clock,
  RotateCcw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNotifications } from "@/contexts/NotificationContext";
import { Notification } from "@/types/notification";
import { cn } from "@/lib/utils";

interface NotificationItemProps {
  notification: Notification;
  compact?: boolean;
  showActions?: boolean;
  onAction?: () => void;
}

export function NotificationItem({
  notification,
  compact = false,
  showActions = true,
  onAction,
}: NotificationItemProps) {
  const { markAsRead, markAsUnread, deleteNotification } = useNotifications();
  const [isLoading, setIsLoading] = useState(false);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ERROR":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "WARNING":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "SYSTEM":
        return <Settings className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "URGENT":
        return (
          <Badge variant="destructive" className="text-xs">
            Khẩn cấp
          </Badge>
        );
      case "HIGH":
        return (
          <Badge variant="secondary" className="text-xs">
            Cao
          </Badge>
        );
      case "NORMAL":
        return (
          <Badge variant="outline" className="text-xs">
            Bình thường
          </Badge>
        );
      case "LOW":
        return (
          <Badge variant="outline" className="text-xs text-muted-foreground">
            Thấp
          </Badge>
        );
      default:
        return null;
    }
  };

  const handleMarkAsRead = async () => {
    if (notification.isRead) return;

    try {
      setIsLoading(true);
      await markAsRead(notification.id);
      onAction?.();
    } catch (err) {
      console.error("Failed to mark as read:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkAsUnread = async () => {
    if (!notification.isRead) return;

    try {
      setIsLoading(true);
      await markAsUnread(notification.id);
      onAction?.();
    } catch (err) {
      console.error("Failed to mark as unread:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      await deleteNotification(notification.id);
      onAction?.();
    } catch (err) {
      console.error("Failed to delete notification:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleActionClick = () => {
    if (!notification.isRead) {
      handleMarkAsRead();
    }
    onAction?.();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} phút trước`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} giờ trước`;
    } else {
      return format(date, "dd/MM/yyyy HH:mm", { locale: vi });
    }
  };

  if (compact) {
    return (
      <div
        className={cn(
          "flex items-start gap-3 p-3 rounded-lg transition-colors hover:bg-muted/50 cursor-pointer",
          !notification.isRead && "bg-blue-50 border-l-4 border-l-blue-500"
        )}
        onClick={handleActionClick}
      >
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          {getTypeIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <p
                className={cn(
                  "text-sm font-medium truncate",
                  !notification.isRead && "font-semibold"
                )}
              >
                {notification.title}
              </p>
              <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                {notification.message}
              </p>
            </div>

            {/* Priority Badge */}
            {notification.priority !== "NORMAL" && (
              <div className="flex-shrink-0">
                {getPriorityBadge(notification.priority)}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{formatTime(notification.createdAt)}</span>
            </div>

            {/* Action Button */}
            {notification.actionUrl && (
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="h-6 px-2 text-xs"
                onClick={(e) => e.stopPropagation()}
              >
                <Link href={notification.actionUrl}>
                  <ExternalLink className="h-3 w-3" />
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Unread Indicator */}
        {!notification.isRead && (
          <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2" />
        )}
      </div>
    );
  }

  return (
    <Card
      className={cn(
        "transition-all duration-200",
        !notification.isRead && "border-l-4 border-l-blue-500 bg-blue-50/50"
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className="flex-shrink-0 mt-1">
            {getTypeIcon(notification.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h4
                className={cn(
                  "text-sm font-medium",
                  !notification.isRead && "font-semibold"
                )}
              >
                {notification.title}
              </h4>

              <div className="flex items-center gap-2">
                {getPriorityBadge(notification.priority)}

                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        disabled={isLoading}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {!notification.isRead ? (
                        <DropdownMenuItem onClick={handleMarkAsRead}>
                          <Check className="h-4 w-4 mr-2" />
                          Đánh dấu đã đọc
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={handleMarkAsUnread}>
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Đánh dấu chưa đọc
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={handleDelete}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>

            <p className="text-sm text-muted-foreground mb-3">
              {notification.message}
            </p>

            {/* Footer */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatTime(notification.createdAt)}</span>
                </div>

                {notification.creator && (
                  <span>bởi {notification.creator.name}</span>
                )}
              </div>

              {/* Action Button */}
              {notification.actionUrl && (
                <Button variant="outline" size="sm" asChild className="h-7">
                  <Link href={notification.actionUrl}>
                    Xem chi tiết
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </Button>
              )}
            </div>
          </div>

          {/* Unread Indicator */}
          {!notification.isRead && (
            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2" />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
