import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { z } from 'zod';

const categorySchema = z.object({
	name: z.string().min(1, 'Tên danh mục là bắt buộc'),
	description: z.string().optional(),
	image: z.string().optional(),
	parentId: z.string().optional(),
});

// GET /api/categories - L<PERSON>y danh sách danh mục
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const includeProducts = searchParams.get('includeProducts') === 'true';
		const parentId = searchParams.get('parentId');

		const where: any = {};
		if (parentId) {
			where.parentId = parentId;
		} else if (parentId === null) {
			where.parentId = null; // Root categories only
		}

		const categories = await prisma.category.findMany({
			where,
			include: {
				children: {
					include: {
						_count: {
							select: {
								products: true,
							},
						},
					},
				},
				parent: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
				_count: {
					select: {
						products: true,
						children: true,
					},
				},
				...(includeProducts && {
					products: {
						where: {
							status: 'ACTIVE',
						},
						select: {
							id: true,
							name: true,
							price: true,
							salePrice: true,
							images: true,
							slug: true,
							featured: true,
						},
						take: 8, // Limit products per category
					},
				}),
			},
			orderBy: {
				name: 'asc',
			},
		});

		return NextResponse.json(categories);
	} catch (error) {
		console.error('Get categories error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách danh mục' },
			{ status: 500 }
		);
	}
}

// POST /api/categories - Tạo danh mục mới (Admin only)
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const body = await request.json();
		const data = categorySchema.parse(body);

		// Generate slug from name
		const slug = data.name
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();

		// Check if slug already exists
		let finalSlug = slug;
		let counter = 1;
		while (await prisma.category.findUnique({ where: { slug: finalSlug } })) {
			finalSlug = `${slug}-${counter}`;
			counter++;
		}

		// Validate parent category if provided
		if (data.parentId) {
			const parentCategory = await prisma.category.findUnique({
				where: { id: data.parentId },
			});

			if (!parentCategory) {
				return NextResponse.json(
					{ error: 'Danh mục cha không tồn tại' },
					{ status: 400 }
				);
			}
		}

		const category = await prisma.category.create({
			data: {
				...data,
				slug: finalSlug,
			},
			include: {
				parent: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
				_count: {
					select: {
						products: true,
						children: true,
					},
				},
			},
		});

		return NextResponse.json(
			{
				message: 'Tạo danh mục thành công',
				category,
			},
			{ status: 201 }
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Create category error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi tạo danh mục' },
			{ status: 500 }
		);
	}
}
