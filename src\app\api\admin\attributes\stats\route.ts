import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";

// GET /api/admin/attributes/stats - <PERSON><PERSON><PERSON> thống kê chi tiết về attributes
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 401 }
      );
    }

    // Get basic counts
    const [
      totalAttributes,
      requiredAttributes,
      filterableAttributes,
      attributesWithValues,
      attributesInUse,
      totalValues,
      totalProductAttributes,
    ] = await Promise.all([
      prisma.attribute.count(),
      prisma.attribute.count({ where: { isRequired: true } }),
      prisma.attribute.count({ where: { isFilterable: true } }),
      prisma.attribute.count({
        where: {
          values: {
            some: {},
          },
        },
      }),
      prisma.attribute.count({
        where: {
          products: {
            some: {},
          },
        },
      }),
      prisma.attributeValue.count(),
      prisma.productAttribute.count(),
    ]);

    // Get attributes by type
    const attributesByType = await prisma.attribute.groupBy({
      by: ["type"],
      _count: {
        _all: true,
      },
    });

    // Get top attributes by usage
    const topAttributesByUsage = await prisma.attribute.findMany({
      include: {
        _count: {
          select: {
            products: true,
            values: true,
          },
        },
      },
      orderBy: {
        products: {
          _count: "desc",
        },
      },
      take: 10,
    });

    // Get attributes with most values
    const attributesWithMostValues = await prisma.attribute.findMany({
      include: {
        _count: {
          select: {
            values: true,
          },
        },
      },
      orderBy: {
        values: {
          _count: "desc",
        },
      },
      take: 10,
    });

    // Get recent attributes
    const recentAttributes = await prisma.attribute.findMany({
      include: {
        _count: {
          select: {
            values: true,
            products: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Get value distribution
    const valueDistribution = await prisma.attributeValue.groupBy({
      by: ["attributeId"],
      _count: {
        _all: true,
      },
      orderBy: {
        _count: {
          attributeId: "desc",
        },
      },
      take: 10,
    });

    // Get attribute names for value distribution
    const attributeIds = valueDistribution.map((item) => item.attributeId);
    const attributeNames = await prisma.attribute.findMany({
      where: {
        id: { in: attributeIds },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const valueDistributionWithNames = valueDistribution.map((item) => {
      const attribute = attributeNames.find(
        (attr) => attr.id === item.attributeId
      );
      return {
        attributeId: item.attributeId,
        attributeName: attribute?.name || "Unknown",
        valueCount: item._count._all,
      };
    });

    // Get monthly growth data (last 12 months)
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const monthlyGrowth = await prisma.attribute.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: {
          gte: twelveMonthsAgo,
        },
      },
      _count: {
        _all: true,
      },
    });

    // Process monthly growth data
    const monthlyGrowthProcessed = monthlyGrowth.reduce(
      (acc, item) => {
        const month = item.createdAt.toISOString().slice(0, 7); // YYYY-MM format
        acc[month] = (acc[month] || 0) + item._count._all;
        return acc;
      },
      {} as Record<string, number>
    );

    // Get unused attributes (attributes with no products)
    const unusedAttributes = await prisma.attribute.findMany({
      where: {
        products: {
          none: {},
        },
      },
      include: {
        _count: {
          select: {
            values: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    // Calculate percentages
    const stats = {
      overview: {
        total: totalAttributes,
        required: requiredAttributes,
        filterable: filterableAttributes,
        withValues: attributesWithValues,
        inUse: attributesInUse,
        unused: totalAttributes - attributesInUse,
        totalValues,
        totalProductAttributes,
      },
      percentages: {
        requiredPercentage:
          totalAttributes > 0
            ? (requiredAttributes / totalAttributes) * 100
            : 0,
        filterablePercentage:
          totalAttributes > 0
            ? (filterableAttributes / totalAttributes) * 100
            : 0,
        withValuesPercentage:
          totalAttributes > 0
            ? (attributesWithValues / totalAttributes) * 100
            : 0,
        inUsePercentage:
          totalAttributes > 0 ? (attributesInUse / totalAttributes) * 100 : 0,
      },
      byType: attributesByType.reduce(
        (acc, item) => {
          acc[item.type] = item._count._all;
          return acc;
        },
        {} as Record<string, number>
      ),
      topByUsage: topAttributesByUsage.map((attr) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        productCount: attr._count.products,
        valueCount: attr._count.values,
        isRequired: attr.isRequired,
        isFilterable: attr.isFilterable,
      })),
      mostValues: attributesWithMostValues.map((attr) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        valueCount: attr._count.values,
      })),
      recent: recentAttributes.map((attr) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        createdAt: attr.createdAt,
        valueCount: attr._count.values,
        productCount: attr._count.products,
      })),
      valueDistribution: valueDistributionWithNames,
      monthlyGrowth: monthlyGrowthProcessed,
      unused: unusedAttributes.map((attr) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        createdAt: attr.createdAt,
        valueCount: attr._count.values,
      })),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching attribute statistics:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thống kê" },
      { status: 500 }
    );
  }
}
