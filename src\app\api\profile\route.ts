import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { z } from 'zod';

const updateProfileSchema = z.object({
	name: z.string().min(1, 'Họ tên là bắt buộc'),
	phone: z.string().optional(),
	dateOfBirth: z.string().optional(),
	gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
});

// GET /api/profile - Lấy thông tin profile của user
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const user = await prisma.user.findUnique({
			where: { id: session.user.id },
			include: {
				addresses: {
					orderBy: {
						isDefault: 'desc',
					},
				},
			},
		});

		if (!user) {
			return NextResponse.json(
				{ error: 'Không tìm thấy người dùng' },
				{ status: 404 }
			);
		}

		// Remove sensitive data
		const { password, ...userProfile } = user;

		return NextResponse.json(userProfile);
	} catch (error) {
		console.error('Get profile error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy thông tin cá nhân' },
			{ status: 500 }
		);
	}
}

// PUT /api/profile - Cập nhật thông tin profile
export async function PUT(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const data = updateProfileSchema.parse(body);

		// Prepare update data
		const updateData: any = {
			name: data.name,
		};

		if (data.phone) {
			updateData.phone = data.phone;
		}

		if (data.dateOfBirth) {
			updateData.dateOfBirth = new Date(data.dateOfBirth);
		}

		if (data.gender) {
			updateData.gender = data.gender;
		}

		const user = await prisma.user.update({
			where: { id: session.user.id },
			data: updateData,
			include: {
				addresses: {
					orderBy: {
						isDefault: 'desc',
					},
				},
			},
		});

		// Remove sensitive data
		const { password, ...userProfile } = user;

		return NextResponse.json(userProfile);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Update profile error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi cập nhật thông tin cá nhân' },
			{ status: 500 }
		);
	}
}
