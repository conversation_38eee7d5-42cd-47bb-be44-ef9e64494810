'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, ThumbsUp, Flag } from 'lucide-react';
import { toast } from 'sonner';

interface Review {
	id: string;
	rating: number;
	comment?: string;
	images: string[];
	createdAt: string;
	user: {
		id: string;
		name: string;
		avatar?: string;
	};
}

interface ReviewListProps {
	productId: string;
	refreshTrigger?: number;
}

export function ReviewList({ productId, refreshTrigger }: ReviewListProps) {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [loading, setLoading] = useState(true);
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 10,
		total: 0,
		pages: 0,
	});

	useEffect(() => {
		fetchReviews();
	}, [productId, pagination.page, refreshTrigger]);

	const fetchReviews = async () => {
		setLoading(true);
		try {
			const params = new URLSearchParams({
				productId,
				page: pagination.page.toString(),
				limit: pagination.limit.toString(),
			});

			const response = await fetch(`/api/reviews?${params}`);
			const data = await response.json();

			if (response.ok) {
				setReviews(data.reviews);
				setPagination(data.pagination);
			} else {
				toast.error('Có lỗi xảy ra khi tải đánh giá');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải đánh giá');
		} finally {
			setLoading(false);
		}
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, i) => (
			<Star
				key={i}
				className={`h-4 w-4 ${
					i < Math.floor(rating)
						? 'text-yellow-400 fill-current'
						: 'text-gray-300'
				}`}
			/>
		));
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('vi-VN', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	if (loading && pagination.page === 1) {
		return (
			<div className="space-y-4">
				{Array.from({ length: 3 }, (_, i) => (
					<Card key={i} className="animate-pulse">
						<CardContent className="p-6">
							<div className="flex items-start gap-4">
								<div className="w-10 h-10 bg-gray-200 rounded-full" />
								<div className="flex-1 space-y-2">
									<div className="h-4 bg-gray-200 rounded" />
									<div className="h-4 bg-gray-200 rounded w-2/3" />
									<div className="h-20 bg-gray-200 rounded" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	if (reviews.length === 0) {
		return (
			<Card>
				<CardContent className="p-8 text-center">
					<Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h3 className="text-lg font-semibold mb-2">Chưa có đánh giá nào</h3>
					<p className="text-muted-foreground">
						Hãy là người đầu tiên đánh giá sản phẩm này
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			{/* Reviews */}
			<div className="space-y-4">
				{reviews.map((review) => (
					<Card key={review.id}>
						<CardContent className="p-6">
							<div className="flex items-start gap-4">
								{/* User Avatar */}
								<div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0">
									{review.user.avatar ? (
										<Image
											src={review.user.avatar}
											alt={review.user.name}
											width={40}
											height={40}
											className="rounded-full"
										/>
									) : (
										<span className="text-pink-600 font-medium">
											{review.user.name.charAt(0).toUpperCase()}
										</span>
									)}
								</div>

								{/* Review Content */}
								<div className="flex-1">
									{/* Header */}
									<div className="flex items-center justify-between mb-2">
										<div>
											<h4 className="font-medium">{review.user.name}</h4>
											<div className="flex items-center gap-2">
												<div className="flex items-center gap-1">
													{renderStars(review.rating)}
												</div>
												<span className="text-sm text-muted-foreground">
													{formatDate(review.createdAt)}
												</span>
											</div>
										</div>

										{/* Actions */}
										<div className="flex items-center gap-2">
											<Button
												variant="ghost"
												size="sm"
												onClick={() => {
													// Like review logic
													toast.success('Tính năng thích đánh giá sẽ sớm có');
												}}
											>
												<ThumbsUp className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => {
													// Report review logic
													toast.success('Tính năng báo cáo sẽ sớm có');
												}}
											>
												<Flag className="h-4 w-4" />
											</Button>
										</div>
									</div>

									{/* Comment */}
									{review.comment && (
										<p className="text-muted-foreground mb-3 leading-relaxed">
											{review.comment}
										</p>
									)}

									{/* Images */}
									{review.images.length > 0 && (
										<div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
											{review.images.map((image, index) => (
												<div
													key={index}
													className="relative aspect-square overflow-hidden rounded-lg bg-gray-100 cursor-pointer hover:opacity-80 transition-opacity"
													onClick={() => {
														// Open image modal logic
														toast.success('Tính năng xem ảnh lớn sẽ sớm có');
													}}
												>
													<Image
														src={image}
														alt={`Review image ${index + 1}`}
														fill
														className="object-cover"
													/>
												</div>
											))}
										</div>
									)}

									{/* Helpful */}
									<div className="flex items-center gap-4 text-sm text-muted-foreground">
										<button
											className="hover:text-foreground transition-colors"
											onClick={() => {
												// Mark as helpful logic
												toast.success('Cảm ơn phản hồi của bạn!');
											}}
										>
											Hữu ích
										</button>
										<button
											className="hover:text-foreground transition-colors"
											onClick={() => {
												// Reply logic
												toast.success('Tính năng trả lời sẽ sớm có');
											}}
										>
											Trả lời
										</button>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Load More */}
			{pagination.page < pagination.pages && (
				<div className="text-center">
					<Button
						variant="outline"
						onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
						disabled={loading}
					>
						{loading ? 'Đang tải...' : 'Xem thêm đánh giá'}
					</Button>
				</div>
			)}

			{/* Pagination Info */}
			<div className="text-center text-sm text-muted-foreground">
				Hiển thị {reviews.length} trong tổng số {pagination.total} đánh giá
			</div>
		</div>
	);
}
