# Admin Pages E2E Tests

This directory contains comprehensive end-to-end tests for the Admin Pages CRUD functionality using Playwright.

## Test Structure

### Test Files

- **`admin-pages-crud.spec.ts`** - Main CRUD operations test suite
  - Create page operations
  - Read/List page operations  
  - Update page operations
  - Delete page operations
  - Bulk operations
  - Form validation
  - Authentication and authorization

- **`admin-pages-permissions.spec.ts`** - Permissions and edge cases
  - Moderator permissions testing
  - Error handling scenarios
  - Edge cases and concurrent operations
  - Session management

### Page Objects

- **`pages/admin-login.page.ts`** - Admin login page interactions
- **`pages/admin-pages.page.ts`** - Admin pages management interface

### Helpers

- **`helpers/pages.helper.ts`** - Database operations and test data management
- **`helpers/page-form.helper.ts`** - Form interactions and validation utilities

## Running the Tests

### Prerequisites

1. Ensure the development server is running:
   ```bash
   npm run dev
   ```

2. Ensure the database is set up and migrations are applied:
   ```bash
   npx prisma migrate dev
   ```

3. Make sure test admin users exist (created by global setup):
   - Admin: `<EMAIL>` / `admin123`
   - Moderator: `<EMAIL>` / `moderator123`

### Run All Admin Pages Tests

```bash
# Run all admin pages tests
npx playwright test tests/e2e/admin-pages*.spec.ts

# Run with specific configuration
npx playwright test --config=tests/e2e/admin-pages.config.ts

# Run in headed mode (see browser)
npx playwright test tests/e2e/admin-pages*.spec.ts --headed

# Run specific test file
npx playwright test tests/e2e/admin-pages-crud.spec.ts

# Run specific test
npx playwright test tests/e2e/admin-pages-crud.spec.ts -g "should create a new page"
```

### Run Tests in Different Browsers

```bash
# Run in Chrome
npx playwright test tests/e2e/admin-pages*.spec.ts --project=chromium-admin-pages

# Run in Firefox
npx playwright test tests/e2e/admin-pages*.spec.ts --project=firefox-admin-pages

# Run in Safari
npx playwright test tests/e2e/admin-pages*.spec.ts --project=webkit-admin-pages

# Run on mobile
npx playwright test tests/e2e/admin-pages*.spec.ts --project=mobile-chrome-admin
```

### Debug Tests

```bash
# Run in debug mode
npx playwright test tests/e2e/admin-pages-crud.spec.ts --debug

# Run with trace viewer
npx playwright test tests/e2e/admin-pages*.spec.ts --trace=on
npx playwright show-trace test-results/trace.zip

# Generate and view HTML report
npx playwright test tests/e2e/admin-pages*.spec.ts
npx playwright show-report test-results/admin-pages-html
```

## Test Coverage

### CRUD Operations

✅ **Create**
- Create page with all fields
- Create page with minimal required fields
- Auto-generate slug from title
- Handle duplicate slug errors
- Form validation for required fields

✅ **Read**
- Display pages list
- Search pages by title/content
- Filter by status (DRAFT/PUBLISHED/ARCHIVED)
- Filter by featured status
- Pagination

✅ **Update**
- Edit existing pages
- Toggle featured status
- Update page status
- Form validation on updates

✅ **Delete**
- Delete single page
- Confirmation dialog
- Bulk delete multiple pages
- Error handling

### Advanced Features

✅ **Bulk Operations**
- Select multiple pages
- Select all/deselect all
- Bulk delete
- Bulk status change

✅ **Form Validation**
- Required field validation
- Slug format validation
- XSS prevention
- Content length handling

✅ **Authentication & Authorization**
- Admin login/logout
- Session management
- Moderator permissions
- Unauthorized access protection

✅ **Edge Cases**
- Network error handling
- Long content handling
- Special characters
- Concurrent operations
- Session timeout
- Browser navigation

## Test Data Management

### Automatic Cleanup

Tests automatically clean up test data:
- Before each test (cleanup existing test data)
- After each test (cleanup created test data)
- Global teardown (cleanup all test data)

### Test Data Patterns

Test pages are identified by:
- Slug starting with `test-page-`
- Title starting with `Test Page` or `E2E Test`
- Created by test admin users

### Database Helpers

```typescript
// Generate test data
const testPage = generateTestPageData({
  title: "My Test Page",
  status: "DRAFT"
});

// Create in database
const createdPage = await createTestPageInDB(testPage, adminId);

// Verify existence
const exists = await verifyPageExistsInDB(slug);

// Clean up
await cleanupTestPages();
```

## Configuration

### Test Configuration

The `admin-pages.config.ts` file provides:
- Specific settings for admin tests
- Increased timeouts for admin operations
- Limited parallelism to avoid conflicts
- Larger viewport for admin interface
- Custom reporters for admin test results

### Environment Variables

```bash
# Base URL for tests
PLAYWRIGHT_BASE_URL=http://localhost:3000

# Database URL for test data operations
DATABASE_URL=postgresql://...

# Admin authentication secrets
NEXTAUTH_SECRET=your-secret-key
```

## Troubleshooting

### Common Issues

1. **Tests fail with "Admin user not found"**
   - Ensure global setup has run: `npx playwright test --global-setup-only`
   - Check database connection and admin user creation

2. **Authentication failures**
   - Verify admin credentials in global setup
   - Check admin login page URL and form selectors
   - Ensure session cookies are properly set

3. **Form submission failures**
   - Check form field selectors in page objects
   - Verify rich text editor interactions
   - Ensure proper wait conditions

4. **Database conflicts**
   - Run tests sequentially: `--workers=1`
   - Ensure proper test data cleanup
   - Check for existing test data conflicts

### Debug Tips

1. **Use headed mode** to see browser interactions:
   ```bash
   npx playwright test --headed --slowMo=1000
   ```

2. **Add debug points** in test code:
   ```typescript
   await page.pause(); // Pauses execution for inspection
   ```

3. **Check network requests**:
   ```typescript
   page.on('request', request => console.log(request.url()));
   page.on('response', response => console.log(response.status()));
   ```

4. **Screenshot on failure**:
   ```typescript
   await page.screenshot({ path: 'debug-screenshot.png' });
   ```

## Contributing

When adding new tests:

1. Follow the existing page object pattern
2. Use the helper functions for data management
3. Include proper cleanup in test hooks
4. Add appropriate assertions and error handling
5. Update this README with new test coverage

### Test Naming Convention

- Test files: `admin-pages-*.spec.ts`
- Test descriptions: Use clear, descriptive names
- Test data: Prefix with "E2E Test" or "Test Page"
- Database cleanup: Use consistent patterns

### Page Object Guidelines

- Keep selectors maintainable and specific
- Use data-testid attributes when possible
- Implement wait conditions for dynamic content
- Provide clear method names and documentation
- Handle different UI states (loading, error, success)
