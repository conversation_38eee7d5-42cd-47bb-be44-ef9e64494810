"use client";

import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Separator,
} from "@/components/ui";
import {
  User,
  Calendar,
  Globe,
  Monitor,
  FileText,
  Hash,
  Activity,
  Database,
} from "lucide-react";

interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  admin: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  oldValues?: any;
  newValues?: any;
}

interface AuditLogDetailProps {
  log: AuditLog;
  onClose: () => void;
}

export function AuditLogDetail({ log, onClose }: AuditLogDetailProps) {
  const getActionBadgeVariant = (action: string) => {
    if (action.includes("CREATE")) return "default";
    if (action.includes("UPDATE")) return "secondary";
    if (action.includes("DELETE")) return "destructive";
    if (action.includes("FAILED")) return "destructive";
    return "outline";
  };

  const formatUserAgent = (userAgent: string) => {
    if (!userAgent || userAgent === "unknown") return "Không xác định";
    
    // Extract browser and OS info
    const browserMatch = userAgent.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/);
    const osMatch = userAgent.match(/(Windows|Mac|Linux|Android|iOS)/);
    
    const browser = browserMatch ? browserMatch[0] : "Unknown Browser";
    const os = osMatch ? osMatch[1] : "Unknown OS";
    
    return `${browser} on ${os}`;
  };

  const renderValueComparison = () => {
    if (!log.oldValues && !log.newValues) return null;

    const changes = getChanges(log.oldValues, log.newValues);
    
    if (changes.length === 0) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Chi tiết thay đổi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {changes.map((change, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{change.field}</Badge>
                <Badge variant={
                  change.type === "added" ? "default" :
                  change.type === "modified" ? "secondary" :
                  "destructive"
                }>
                  {change.type === "added" ? "Thêm mới" :
                   change.type === "modified" ? "Thay đổi" :
                   "Xóa"}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {change.type !== "added" && (
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Giá trị cũ:
                    </div>
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <code className="text-sm">
                        {change.oldValue === null ? "null" : 
                         typeof change.oldValue === "object" ? 
                         JSON.stringify(change.oldValue, null, 2) : 
                         String(change.oldValue)}
                      </code>
                    </div>
                  </div>
                )}
                
                {change.type !== "removed" && (
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Giá trị mới:
                    </div>
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <code className="text-sm">
                        {change.newValue === null ? "null" : 
                         typeof change.newValue === "object" ? 
                         JSON.stringify(change.newValue, null, 2) : 
                         String(change.newValue)}
                      </code>
                    </div>
                  </div>
                )}
              </div>
              
              {index < changes.length - 1 && <Separator />}
            </div>
          ))}
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Chi tiết Audit Log
          </DialogTitle>
          <DialogDescription>
            Thông tin chi tiết về hoạt động được ghi lại
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Thông tin cơ bản
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">ID:</span>
                  </div>
                  <div className="font-mono text-sm bg-muted p-2 rounded">
                    {log.id}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Thời gian:</span>
                  </div>
                  <div className="text-sm">
                    {format(new Date(log.createdAt), "dd/MM/yyyy HH:mm:ss", {
                      locale: vi,
                    })}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Hành động:</span>
                  </div>
                  <Badge variant={getActionBadgeVariant(log.action)}>
                    {log.action}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Tài nguyên:</span>
                  </div>
                  <div className="text-sm">
                    {log.resource}
                    {log.resourceId && (
                      <span className="text-muted-foreground ml-2">
                        (ID: {log.resourceId})
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {log.description && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Mô tả:</span>
                  </div>
                  <div className="text-sm p-3 bg-muted rounded">
                    {log.description}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Admin Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Thông tin Admin
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={log.admin.avatar} />
                  <AvatarFallback>
                    {log.admin.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="font-medium">{log.admin.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {log.admin.email}
                  </div>
                  <Badge variant="outline">{log.admin.role}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Thông tin kỹ thuật
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Địa chỉ IP:</span>
                  </div>
                  <div className="font-mono text-sm">
                    {log.ipAddress || "Không xác định"}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Trình duyệt:</span>
                  </div>
                  <div className="text-sm">
                    {formatUserAgent(log.userAgent || "")}
                  </div>
                </div>
              </div>

              {log.userAgent && log.userAgent !== "unknown" && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">User Agent đầy đủ:</div>
                  <div className="font-mono text-xs p-3 bg-muted rounded break-all">
                    {log.userAgent}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Value Changes */}
          {renderValueComparison()}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to compare old and new values
function getChanges(oldValues: any, newValues: any) {
  const changes: Array<{
    field: string;
    oldValue: any;
    newValue: any;
    type: "added" | "modified" | "removed";
  }> = [];

  const allKeys = new Set([
    ...Object.keys(oldValues || {}),
    ...Object.keys(newValues || {}),
  ]);

  for (const key of allKeys) {
    const oldValue = oldValues?.[key];
    const newValue = newValues?.[key];

    if (oldValue === undefined && newValue !== undefined) {
      changes.push({
        field: key,
        oldValue: null,
        newValue,
        type: "added",
      });
    } else if (oldValue !== undefined && newValue === undefined) {
      changes.push({
        field: key,
        oldValue,
        newValue: null,
        type: "removed",
      });
    } else if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
      changes.push({
        field: key,
        oldValue,
        newValue,
        type: "modified",
      });
    }
  }

  return changes;
}
