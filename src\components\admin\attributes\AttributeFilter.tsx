"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Filter,
  X,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Search,
  SlidersHorizontal,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Attribute,
  AttributeValue,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

interface AttributeFilterProps {
  attributes: Attribute[];
  selectedFilters: Record<string, string[]>;
  onFiltersChange: (filters: Record<string, string[]>) => void;
  className?: string;
  showMobileSheet?: boolean;
  productCount?: number;
}

interface FilterGroupProps {
  attribute: Attribute;
  selectedValues: string[];
  onSelectionChange: (attributeId: string, values: string[]) => void;
  showCount?: boolean;
}

function FilterGroup({
  attribute,
  selectedValues,
  onSelectionChange,
  showCount = true,
}: FilterGroupProps) {
  const [isOpen, setIsOpen] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAll, setShowAll] = useState(false);

  const filteredValues = attribute.values?.filter((value) =>
    value.value.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const displayValues = showAll ? filteredValues : filteredValues.slice(0, 5);
  const hasMore = filteredValues.length > 5;

  const handleValueToggle = (valueId: string) => {
    const newValues = selectedValues.includes(valueId)
      ? selectedValues.filter((id) => id !== valueId)
      : [...selectedValues, valueId];
    
    onSelectionChange(attribute.id, newValues);
  };

  const getColorCode = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      "đỏ": "#ef4444",
      "xanh dương": "#3b82f6",
      "xanh lá": "#22c55e",
      "vàng": "#eab308",
      "đen": "#000000",
      "trắng": "#ffffff",
      "hồng": "#ec4899",
      "tím": "#a855f7",
      "xám": "#6b7280",
      "nâu": "#a3a3a3",
      "cam": "#f97316",
      "be": "#f5f5dc",
      "navy": "#1e3a8a",
      "khaki": "#f0e68c",
    };
    return colorMap[colorName.toLowerCase()] || "#6b7280";
  };

  const renderValue = (value: AttributeValue) => {
    switch (attribute.type) {
      case "COLOR":
        return (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-gray-300"
              style={{
                backgroundColor: getColorCode(value.value),
              }}
            />
            <span>{value.value}</span>
          </div>
        );
      case "SIZE":
        return (
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm bg-gray-100 px-2 py-1 rounded">
              {value.value}
            </span>
          </div>
        );
      default:
        return <span>{value.value}</span>;
    }
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between p-0 h-auto font-medium"
        >
          <span>{attribute.name}</span>
          <div className="flex items-center gap-2">
            {selectedValues.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {selectedValues.length}
              </Badge>
            )}
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </div>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-3 mt-3">
        {/* Search for values */}
        {(attribute.values?.length || 0) > 5 && (
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Tìm ${attribute.name.toLowerCase()}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-9"
            />
          </div>
        )}

        {/* Values list */}
        <div className="space-y-2">
          {displayValues
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((value) => (
              <div key={value.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`${attribute.id}-${value.id}`}
                  checked={selectedValues.includes(value.id)}
                  onCheckedChange={() => handleValueToggle(value.id)}
                />
                <Label
                  htmlFor={`${attribute.id}-${value.id}`}
                  className="flex-1 cursor-pointer text-sm"
                >
                  <div className="flex items-center justify-between">
                    {renderValue(value)}
                    {showCount && value._count?.products && (
                      <span className="text-xs text-muted-foreground">
                        ({value._count.products})
                      </span>
                    )}
                  </div>
                </Label>
              </div>
            ))}
        </div>

        {/* Show more/less button */}
        {hasMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className="w-full text-xs"
          >
            {showAll ? "Ẩn bớt" : `Xem thêm ${filteredValues.length - 5} mục`}
          </Button>
        )}

        {/* Clear selection */}
        {selectedValues.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSelectionChange(attribute.id, [])}
            className="w-full text-xs text-red-600 hover:text-red-700"
          >
            <X className="h-3 w-3 mr-1" />
            Xóa lựa chọn
          </Button>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
}

export function AttributeFilter({
  attributes,
  selectedFilters,
  onFiltersChange,
  className,
  showMobileSheet = true,
  productCount,
}: AttributeFilterProps) {
  const [priceRange, setPriceRange] = useState<{ min: string; max: string }>({
    min: "",
    max: "",
  });

  const filterableAttributes = attributes.filter((attr) => attr.isFilterable);
  const activeFilterCount = Object.values(selectedFilters).reduce(
    (count, values) => count + values.length,
    0
  );

  const handleFilterChange = (attributeId: string, values: string[]) => {
    const newFilters = { ...selectedFilters };
    
    if (values.length === 0) {
      delete newFilters[attributeId];
    } else {
      newFilters[attributeId] = values;
    }
    
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    onFiltersChange({});
    setPriceRange({ min: "", max: "" });
  };

  const getSelectedValueNames = (attributeId: string): string[] => {
    const attribute = attributes.find((attr) => attr.id === attributeId);
    const selectedValueIds = selectedFilters[attributeId] || [];
    
    return selectedValueIds
      .map((valueId) => {
        const value = attribute?.values?.find((v) => v.id === valueId);
        return value?.value || "";
      })
      .filter(Boolean);
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold">Bộ lọc</h3>
          {productCount !== undefined && (
            <p className="text-sm text-muted-foreground">
              {productCount} sản phẩm
            </p>
          )}
        </div>
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            Xóa tất cả
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFilterCount > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Đang áp dụng:</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(selectedFilters).map(([attributeId, valueIds]) => {
              const attribute = attributes.find((attr) => attr.id === attributeId);
              const valueNames = getSelectedValueNames(attributeId);
              
              return valueNames.map((valueName, index) => (
                <Badge
                  key={`${attributeId}-${index}`}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <span className="text-xs">
                    {attribute?.name}: {valueName}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => {
                      const valueId = attribute?.values?.find(
                        (v) => v.value === valueName
                      )?.id;
                      if (valueId) {
                        const newValues = valueIds.filter((id) => id !== valueId);
                        handleFilterChange(attributeId, newValues);
                      }
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ));
            })}
          </div>
        </div>
      )}

      <Separator />

      {/* Price Range Filter */}
      <div className="space-y-3">
        <h4 className="font-medium">Khoảng giá</h4>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="min-price" className="text-xs">
              Từ
            </Label>
            <Input
              id="min-price"
              type="number"
              placeholder="0"
              value={priceRange.min}
              onChange={(e) =>
                setPriceRange((prev) => ({ ...prev, min: e.target.value }))
              }
              className="h-9"
            />
          </div>
          <div>
            <Label htmlFor="max-price" className="text-xs">
              Đến
            </Label>
            <Input
              id="max-price"
              type="number"
              placeholder="1000000"
              value={priceRange.max}
              onChange={(e) =>
                setPriceRange((prev) => ({ ...prev, max: e.target.value }))
              }
              className="h-9"
            />
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => {
            // Handle price filter application
            console.log("Apply price filter:", priceRange);
          }}
        >
          Áp dụng
        </Button>
      </div>

      <Separator />

      {/* Attribute Filters */}
      <div className="space-y-4">
        {filterableAttributes.map((attribute) => (
          <FilterGroup
            key={attribute.id}
            attribute={attribute}
            selectedValues={selectedFilters[attribute.id] || []}
            onSelectionChange={handleFilterChange}
          />
        ))}
      </div>

      {/* No filterable attributes */}
      {filterableAttributes.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Filter className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Không có thuộc tính nào có thể lọc</p>
        </div>
      )}
    </div>
  );

  // Mobile version with sheet
  if (showMobileSheet) {
    return (
      <>
        {/* Desktop version */}
        <div className={cn("hidden lg:block", className)}>
          <Card>
            <CardContent className="p-6">
              <FilterContent />
            </CardContent>
          </Card>
        </div>

        {/* Mobile version */}
        <div className="lg:hidden">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="w-full">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Bộ lọc
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <SheetHeader>
                <SheetTitle>Bộ lọc sản phẩm</SheetTitle>
                <SheetDescription>
                  Lọc sản phẩm theo thuộc tính và giá
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6">
                <FilterContent />
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </>
    );
  }

  // Desktop only version
  return (
    <div className={className}>
      <Card>
        <CardContent className="p-6">
          <FilterContent />
        </CardContent>
      </Card>
    </div>
  );
}
