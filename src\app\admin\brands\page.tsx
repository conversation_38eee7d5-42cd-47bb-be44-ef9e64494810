"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Globe,
  Package,
  MoreHorizontal,
  Star,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Brand, BrandFilters, PaginationParams } from "@/types";
import { AdminBrandLogo } from "@/components/admin/AdminImage";

interface BrandsResponse {
  success: boolean;
  data: (Brand & { _count: { products: number } })[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

export default function AdminBrandsPage() {
  const [brands, setBrands] = useState<
    (Brand & { _count: { products: number } })[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<BrandFilters>({
    search: "",
    isActive: undefined,
  });
  const [pagination, setPagination] = useState<
    PaginationParams & {
      page: number;
      limit: number;
      total?: number;
      totalPages?: number;
    }
  >({
    page: 1,
    limit: 10,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Fetch brands
  const fetchBrands = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.isActive !== undefined && {
          isActive: filters.isActive.toString(),
        }),
        sortBy: pagination.sortBy || "createdAt",
        sortOrder: pagination.sortOrder || "desc",
      });

      const response = await fetch(`/api/admin/brands?${params}`);
      const data: BrandsResponse = await response.json();

      if (response.ok && data.success) {
        setBrands(data.data || []);
        setPagination((prev) => ({ ...prev, ...data.pagination }));
      } else {
        toast.error(
          data.error || "Có lỗi xảy ra khi tải danh sách thương hiệu"
        );
      }
    } catch (error) {
      console.error("Fetch brands error:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách thương hiệu");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBrands();
  }, [filters, pagination.page, pagination.sortBy, pagination.sortOrder]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchBrands();
  };

  // Handle filter change
  const handleFilterChange = (key: keyof BrandFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle delete brand
  const handleDeleteBrand = async (brandId: string, brandName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa thương hiệu "${brandName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/brands/${brandId}`, {
        method: "DELETE",
      });
      const data = await response.json();

      if (response.ok && data.success) {
        toast.success("Thương hiệu đã được xóa thành công");
        fetchBrands();
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi xóa thương hiệu");
      }
    } catch (error) {
      console.error("Delete brand error:", error);
      toast.error("Có lỗi xảy ra khi xóa thương hiệu");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý thương hiệu</h1>
          <p className="text-muted-foreground">
            Quản lý thương hiệu sản phẩm trong cửa hàng
          </p>
        </div>
        <Link href="/admin/brands/create">
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Plus className="h-4 w-4 mr-2" />
            Thêm thương hiệu
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="flex gap-4 items-end">
            <div className="flex-1">
              <Input
                placeholder="Tìm kiếm thương hiệu..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.isActive?.toString() || "all"}
              onValueChange={(value) =>
                handleFilterChange(
                  "isActive",
                  value === "all" ? undefined : value === "true"
                )
              }
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="true">Đang hoạt động</SelectItem>
                <SelectItem value="false">Tạm dừng</SelectItem>
              </SelectContent>
            </Select>
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Brands List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-pink-600" />
            Danh sách thương hiệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
            </div>
          ) : brands.length === 0 ? (
            <div className="text-center py-12">
              <Star className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Chưa có thương hiệu nào
              </h3>
              <p className="text-muted-foreground mb-4">
                Bắt đầu bằng cách thêm thương hiệu đầu tiên
              </p>
              <Link href="/admin/brands/create">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm thương hiệu
                </Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Thương hiệu</th>
                    <th className="text-left py-3 px-4">Mô tả</th>
                    <th className="text-left py-3 px-4">Website</th>
                    <th className="text-left py-3 px-4">Sản phẩm</th>
                    <th className="text-left py-3 px-4">Trạng thái</th>
                    <th className="text-left py-3 px-4">Ngày tạo</th>
                    <th className="text-left py-3 px-4">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {brands.map((brand) => (
                    <tr key={brand.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <AdminBrandLogo
                            src={brand.logo}
                            alt={brand.name}
                            size={48}
                          />
                          <div>
                            <p className="font-medium">{brand.name}</p>
                            <p className="text-sm text-gray-500">
                              {brand.slug}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-sm text-gray-600 max-w-xs truncate">
                          {brand.description || "—"}
                        </p>
                      </td>
                      <td className="py-3 px-4">
                        {brand.website ? (
                          <a
                            href={brand.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                          >
                            <Globe className="h-4 w-4" />
                            <span className="text-sm">Website</span>
                          </a>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-1">
                          <Package className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {brand._count.products}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge
                          variant={brand.isActive ? "default" : "secondary"}
                          className={
                            brand.isActive ? "bg-green-100 text-green-800" : ""
                          }
                        >
                          {brand.isActive ? "Hoạt động" : "Tạm dừng"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm text-gray-600">
                          {new Date(brand.createdAt).toLocaleDateString(
                            "vi-VN"
                          )}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/brands/${brand.id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                Xem chi tiết
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/brands/${brand.id}/edit`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Chỉnh sửa
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                handleDeleteBrand(brand.id, brand.name)
                              }
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              disabled={brand._count.products > 0}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Xóa
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {!loading && brands.length > 0 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Hiển thị {(pagination.page - 1) * pagination.limit + 1} đến{" "}
            {Math.min(
              pagination.page * pagination.limit,
              pagination.total || 0
            )}{" "}
            trong tổng số {pagination.total || 0} thương hiệu
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
              }
              disabled={pagination.page <= 1}
            >
              Trước
            </Button>
            <span className="text-sm">
              Trang {pagination.page} / {pagination.totalPages || 1}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
              }
              disabled={pagination.page >= (pagination.totalPages || 1)}
            >
              Sau
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
