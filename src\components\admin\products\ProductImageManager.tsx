"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  X,
  Image as ImageIcon,
  Upload,
  Link as LinkIcon,
  GripVertical,
  Eye,
  AlertCircle,
} from "lucide-react";
import { MediaSelector } from "@/components/admin/MediaManager";
import { MediaType, isValidMediaUrl } from "@/types/media";

interface ProductImage {
  url: string;
  type: MediaType;
  externalUrl?: string;
}

interface ProductImageManagerProps {
  images: ProductImage[];
  onChange: (images: ProductImage[]) => void;
  className?: string;
  maxImages?: number;
}

export function ProductImageManager({
  images,
  onChange,
  className,
  maxImages = 10,
}: ProductImageManagerProps) {
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);

  const handleAddImage = () => {
    if (images.length >= maxImages) return;

    const newImage: ProductImage = {
      url: "",
      type: "INTERNAL",
    };
    onChange([...images, newImage]);
  };

  const handleRemoveImage = (index: number) => {
    setDeleteIndex(index);
  };

  const confirmDelete = () => {
    if (deleteIndex !== null) {
      const newImages = images.filter((_, i) => i !== deleteIndex);
      onChange(newImages);
      setDeleteIndex(null);
    }
  };

  const handleImageChange = (
    index: number,
    field: keyof ProductImage,
    value: string
  ) => {
    const newImages = images.map((img, i) => {
      if (i === index) {
        const updated = { ...img, [field]: value };

        // Clear opposite field when type changes
        if (field === "type") {
          if (value === "INTERNAL") {
            updated.externalUrl = undefined;
          } else {
            updated.url = "";
          }
        }

        return updated;
      }
      return img;
    });
    onChange(newImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= images.length) return;

    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onChange(newImages);
  };

  const getImageUrl = (image: ProductImage) => {
    return image.type === "EXTERNAL" ? image.externalUrl : image.url;
  };

  const isValidImage = (image: ProductImage) => {
    const url = getImageUrl(image);
    return url && isValidMediaUrl(url, image.type);
  };

  const isImageUrl = (url: string) => {
    if (!url) return false;
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <ImageIcon className="h-5 w-5 mr-2" />
              Hình ảnh sản phẩm ({images.length}/{maxImages})
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddImage}
              disabled={images.length >= maxImages}
            >
              <Plus className="h-4 w-4 mr-2" />
              Thêm ảnh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {images.length > 0 ? (
            <div className="space-y-4">
              {images.map((image, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start gap-4">
                    {/* Drag Handle */}
                    <div className="flex flex-col gap-1 mt-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveImage(index, index - 1)}
                        disabled={index === 0}
                        className="h-6 w-6 p-0"
                      >
                        ↑
                      </Button>
                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveImage(index, index + 1)}
                        disabled={index === images.length - 1}
                        className="h-6 w-6 p-0"
                      >
                        ↓
                      </Button>
                    </div>

                    {/* Image Configuration */}
                    <div className="flex-1 space-y-4">
                      <div className="flex items-center gap-2">
                        <Badge variant={index === 0 ? "default" : "secondary"}>
                          {index === 0 ? "Ảnh chính" : `Ảnh ${index + 1}`}
                        </Badge>
                        <Badge
                          variant={
                            image.type === "INTERNAL" ? "default" : "outline"
                          }
                        >
                          {image.type === "INTERNAL" ? "Internal" : "External"}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Loại media</Label>
                          <Select
                            value={image.type}
                            onValueChange={(value: MediaType) =>
                              handleImageChange(index, "type", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="INTERNAL">
                                <div className="flex items-center gap-2">
                                  <Upload className="h-4 w-4" />
                                  <span>Tải lên (Internal)</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="EXTERNAL">
                                <div className="flex items-center gap-2">
                                  <LinkIcon className="h-4 w-4" />
                                  <span>Liên kết ngoài (External)</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          {image.type === "INTERNAL" ? (
                            <div>
                              <Label>Chọn file từ thư viện</Label>
                              <MediaSelector
                                value={image.url}
                                onChange={(url) =>
                                  handleImageChange(index, "url", url)
                                }
                                folder="products"
                                allowedTypes={[
                                  "image/jpeg",
                                  "image/png",
                                  "image/gif",
                                  "image/webp",
                                ]}
                                placeholder="Chọn ảnh..."
                              />
                            </div>
                          ) : (
                            <div>
                              <Label>URL liên kết ngoài</Label>
                              <Input
                                type="url"
                                value={image.externalUrl || ""}
                                onChange={(e) =>
                                  handleImageChange(
                                    index,
                                    "externalUrl",
                                    e.target.value
                                  )
                                }
                                placeholder="https://example.com/image.jpg"
                              />
                              {image.externalUrl && !isValidImage(image) && (
                                <div className="flex items-center gap-2 text-red-600 text-sm mt-1">
                                  <AlertCircle className="h-4 w-4" />
                                  <span>URL không hợp lệ</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Preview */}
                      {isValidImage(image) && (
                        <div className="border rounded p-2">
                          <div className="flex items-center gap-2 mb-2">
                            <Eye className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              Xem trước
                            </span>
                          </div>
                          {isImageUrl(getImageUrl(image)!) ? (
                            <img
                              src={getImageUrl(image)}
                              alt={`Product image ${index + 1}`}
                              className="max-w-full h-auto max-h-32 rounded border"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = "none";
                              }}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-24 bg-gray-100 rounded border">
                              <div className="text-center">
                                <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                                <p className="text-sm text-gray-500">
                                  Không thể xem trước
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Remove Button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveImage(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <ImageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Chưa có hình ảnh nào</p>
              <p className="text-sm">Nhấn &quot;Thêm ảnh&quot; để bắt đầu</p>
            </div>
          )}

          {images.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>Lưu ý:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• Ảnh đầu tiên sẽ được sử dụng làm ảnh chính</li>
                  <li>• Sử dụng nút mũi tên để thay đổi thứ tự ảnh</li>
                  <li>• Hỗ trợ tối đa {maxImages} ảnh</li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={deleteIndex !== null}
        onOpenChange={() => setDeleteIndex(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa ảnh</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa ảnh này? Hành động này không thể hoàn
              tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
