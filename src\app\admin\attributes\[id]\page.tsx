"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowLeft,
  Edit,
  Trash2,
  Package,
  Tag,
  Settings,
  Calendar,
  AlertCircle,
  Loader2,
  Eye,
  Palette,
  Ruler,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Attribute,
  AttributeValue,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export default function AttributeDetailPage() {
  const router = useRouter();
  const params = useParams();
  const attributeId = params.id as string;

  const [attribute, setAttribute] = useState<Attribute | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Fetch attribute data
  const fetchAttribute = async () => {
    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}`);
      if (response.ok) {
        const data = await response.json();
        setAttribute(data);
      } else {
        toast.error("Không tìm thấy thuộc tính");
        router.push("/admin/attributes");
      }
    } catch (error) {
      console.error("Error fetching attribute:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin thuộc tính");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (attributeId) {
      fetchAttribute();
    }
  }, [attributeId]);

  const handleDelete = async () => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Xóa thuộc tính thành công");
        router.push("/admin/attributes");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi xóa thuộc tính");
      }
    } catch (error) {
      console.error("Error deleting attribute:", error);
      toast.error("Có lỗi xảy ra khi xóa thuộc tính");
    } finally {
      setDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const getColorCode = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      "đỏ": "#ef4444",
      "xanh dương": "#3b82f6",
      "xanh lá": "#22c55e",
      "vàng": "#eab308",
      "đen": "#000000",
      "trắng": "#ffffff",
      "hồng": "#ec4899",
      "tím": "#a855f7",
      "xám": "#6b7280",
      "nâu": "#a3a3a3",
      "cam": "#f97316",
      "be": "#f5f5dc",
      "navy": "#1e3a8a",
      "khaki": "#f0e68c",
    };
    return colorMap[colorName.toLowerCase()] || "#6b7280";
  };

  const renderValue = (value: AttributeValue) => {
    switch (attribute?.type) {
      case "COLOR":
        return (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-gray-300"
              style={{
                backgroundColor: getColorCode(value.value),
              }}
            />
            <span>{value.value}</span>
          </div>
        );
      case "SIZE":
        return (
          <div className="flex items-center gap-2">
            <Ruler className="w-4 h-4 text-gray-500" />
            <Badge variant="outline" className="font-medium">
              {value.value}
            </Badge>
          </div>
        );
      default:
        return <span>{value.value}</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Đang tải thông tin thuộc tính...</p>
        </div>
      </div>
    );
  }

  if (!attribute) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
        <h2 className="text-xl font-semibold mb-2">Không tìm thấy thuộc tính</h2>
        <p className="text-muted-foreground mb-4">
          Thuộc tính bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
        </p>
        <Link href="/admin/attributes">
          <Button>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại danh sách
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/attributes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{attribute.name}</h1>
            <p className="text-muted-foreground">
              Chi tiết thuộc tính và danh sách giá trị
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Link href={`/admin/attributes/${attributeId}/edit`}>
            <Button size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          </Link>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Xóa
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Thông tin cơ bản
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-2">Tên thuộc tính</h4>
              <p className="text-muted-foreground">{attribute.name}</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Slug</h4>
              <Badge variant="outline">{attribute.slug}</Badge>
            </div>
            <div>
              <h4 className="font-medium mb-2">Loại</h4>
              <Badge variant="secondary">
                {ATTRIBUTE_TYPE_LABELS[attribute.type]}
              </Badge>
            </div>
            <div>
              <h4 className="font-medium mb-2">Thứ tự sắp xếp</h4>
              <p className="text-muted-foreground">{attribute.sortOrder}</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Trạng thái</h4>
              <div className="flex gap-2">
                {attribute.isRequired && (
                  <Badge variant="destructive">Bắt buộc</Badge>
                )}
                {attribute.isFilterable && (
                  <Badge variant="default">Có thể lọc</Badge>
                )}
                {!attribute.isRequired && !attribute.isFilterable && (
                  <Badge variant="outline">Tùy chọn</Badge>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Ngày tạo</h4>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>
                  {new Date(attribute.createdAt).toLocaleDateString("vi-VN")}
                </span>
              </div>
            </div>
          </div>
          {attribute.description && (
            <div className="mt-6">
              <h4 className="font-medium mb-2">Mô tả</h4>
              <p className="text-muted-foreground">{attribute.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Tag className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Số giá trị</p>
                <p className="text-2xl font-bold">
                  {attribute._count?.values || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Package className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Sản phẩm sử dụng</p>
                <p className="text-2xl font-bold">
                  {attribute._count?.products || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Eye className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Lần cập nhật cuối</p>
                <p className="text-sm font-medium">
                  {new Date(attribute.updatedAt).toLocaleDateString("vi-VN")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Values List */}
      {attribute.values && attribute.values.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Danh sách giá trị ({attribute.values.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Giá trị</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Thứ tự</TableHead>
                  <TableHead>Sản phẩm</TableHead>
                  <TableHead>Ngày tạo</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {attribute.values
                  .sort((a, b) => a.sortOrder - b.sortOrder)
                  .map((value) => (
                    <TableRow key={value.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {renderValue(value)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{value.slug}</Badge>
                      </TableCell>
                      <TableCell>{value.sortOrder}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {value._count?.products || 0}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(value.createdAt).toLocaleDateString("vi-VN")}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* No Values */}
      {(!attribute.values || attribute.values.length === 0) && (
        <Card>
          <CardContent className="text-center py-8">
            <Tag className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Chưa có giá trị nào</p>
            <p className="text-sm text-muted-foreground mb-4">
              Thuộc tính này chưa có giá trị nào được định nghĩa
            </p>
            <Link href={`/admin/attributes/${attributeId}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                Thêm giá trị
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {/* Usage Warning */}
      {attribute._count?.products && attribute._count.products > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-orange-800 mb-1">
                  Thuộc tính đang được sử dụng
                </h4>
                <p className="text-sm text-orange-700">
                  Thuộc tính này đang được sử dụng bởi{" "}
                  <strong>{attribute._count.products}</strong> sản phẩm.
                  Hãy cẩn thận khi thực hiện các thay đổi.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa thuộc tính</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa thuộc tính "{attribute.name}"?
              {attribute._count?.products && attribute._count.products > 0 && (
                <span className="block mt-2 text-red-600">
                  Cảnh báo: Thuộc tính này đang được sử dụng bởi{" "}
                  {attribute._count.products} sản phẩm. Việc xóa sẽ ảnh hưởng
                  đến các sản phẩm này.
                </span>
              )}
              {attribute._count?.values && attribute._count.values > 0 && (
                <span className="block mt-1 text-orange-600">
                  Thuộc tính này có {attribute._count.values} giá trị sẽ bị xóa
                  cùng.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deleting}
            >
              Hủy
            </Button>
            <Button
              onClick={handleDelete}
              variant="destructive"
              disabled={deleting}
            >
              {deleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa thuộc tính
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
