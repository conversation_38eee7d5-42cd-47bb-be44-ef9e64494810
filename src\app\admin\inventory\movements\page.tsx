"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Search,
  History,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Settings,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface StockMovement {
  id: string;
  type: "IN" | "OUT" | "TRANSFER" | "ADJUSTMENT";
  quantity: number;
  reason: string;
  reference?: string;
  notes?: string;
  createdAt: string;
  inventoryEntry: {
    product: {
      name: string;
      sku: string;
    };
  };
}

const movementTypeConfig = {
  IN: { label: "Nhập kho", icon: TrendingUp, color: "bg-green-500" },
  OUT: { label: "Xuất kho", icon: TrendingDown, color: "bg-red-500" },
  TRANSFER: { label: "Chuyển kho", icon: RotateCcw, color: "bg-blue-500" },
  ADJUSTMENT: { label: "Điều chỉnh", icon: Settings, color: "bg-yellow-500" },
};

export default function InventoryMovementsPage() {
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    type: "all",
    page: 1,
    limit: 20,
  });

  // Fetch movements
  useEffect(() => {
    const fetchMovements = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams();
        if (filters.search) params.append("search", filters.search);
        if (filters.type && filters.type !== "all")
          params.append("type", filters.type);
        params.append("page", filters.page.toString());
        params.append("limit", filters.limit.toString());

        const response = await fetch(
          `/api/admin/inventory/movements?${params}`
        );
        if (response.ok) {
          const data = await response.json();
          setMovements(data.data || []);
        }
      } catch (error) {
        console.error("Error fetching movements:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMovements();
  }, [filters]);

  const handleSearch = () => {
    setFilters((prev) => ({ ...prev, page: 1 }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/inventory">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Lịch sử giao dịch kho</h1>
            <p className="text-muted-foreground">
              Theo dõi tất cả các giao dịch nhập xuất kho
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Tìm kiếm sản phẩm, SKU, lý do..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
              />
            </div>
            <Select
              value={filters.type}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, type: value }))
              }
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Loại giao dịch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="IN">Nhập kho</SelectItem>
                <SelectItem value="OUT">Xuất kho</SelectItem>
                <SelectItem value="TRANSFER">Chuyển kho</SelectItem>
                <SelectItem value="ADJUSTMENT">Điều chỉnh</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Movements List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <History className="h-5 w-5 mr-2" />
            Lịch sử giao dịch
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Đang tải...</p>
            </div>
          ) : movements.length === 0 ? (
            <div className="text-center py-8">
              <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Chưa có giao dịch nào
              </h3>
              <p className="text-muted-foreground">
                Các giao dịch nhập xuất kho sẽ hiển thị ở đây
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {movements.map((movement) => {
                const config = movementTypeConfig[movement.type];
                const Icon = config.icon;

                return (
                  <div
                    key={movement.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div
                        className={`p-2 rounded-full ${config.color} text-white`}
                      >
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">
                            {movement.inventoryEntry.product.name}
                          </h4>
                          <Badge variant="outline">
                            {movement.inventoryEntry.product.sku}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {movement.reason}
                          {movement.reference && ` - ${movement.reference}`}
                        </p>
                        {movement.notes && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {movement.notes}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={
                            movement.type === "IN" ? "default" : "destructive"
                          }
                        >
                          {movement.type === "IN" ? "+" : "-"}
                          {movement.quantity}
                        </Badge>
                        <Badge variant="outline">{config.label}</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {format(
                          new Date(movement.createdAt),
                          "dd/MM/yyyy HH:mm",
                          { locale: vi }
                        )}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
