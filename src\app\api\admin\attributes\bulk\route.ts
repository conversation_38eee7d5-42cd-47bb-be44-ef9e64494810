import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schemas for bulk operations
const bulkDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, "Phải chọn ít nhất một thuộc tính"),
});

const bulkUpdateSchema = z.object({
  ids: z.array(z.string()).min(1, "<PERSON><PERSON><PERSON> chọn ít nhất một thuộc tính"),
  data: z.object({
    isRequired: z.boolean().optional(),
    isFilterable: z.boolean().optional(),
    sortOrder: z.number().optional(),
  }),
});

const bulkReorderSchema = z.object({
  attributes: z.array(
    z.object({
      id: z.string(),
      sortOrder: z.number(),
    })
  ),
});

// POST /api/admin/attributes/bulk - Bulk operations for attributes
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "delete":
        return await handleBulkDelete(body);
      case "update":
        return await handleBulkUpdate(body);
      case "reorder":
        return await handleBulkReorder(body);
      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in bulk operation:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}

async function handleBulkDelete(body: any) {
  const validatedData = bulkDeleteSchema.parse(body);

  // Check if any attributes are being used by products
  const attributesInUse = await prisma.attribute.findMany({
    where: {
      id: { in: validatedData.ids },
    },
    include: {
      _count: {
        select: {
          products: true,
        },
      },
    },
  });

  const attributesWithProducts = attributesInUse.filter(
    (attr) => attr._count.products > 0
  );

  if (attributesWithProducts.length > 0) {
    return NextResponse.json(
      {
        error: `Không thể xóa ${attributesWithProducts.length} thuộc tính vì đang được sử dụng bởi sản phẩm`,
        attributesInUse: attributesWithProducts.map((attr) => ({
          id: attr.id,
          name: attr.name,
          productCount: attr._count.products,
        })),
      },
      { status: 400 }
    );
  }

  // Delete attributes in transaction
  const result = await prisma.$transaction(async (tx) => {
    // First delete all attribute values
    await tx.attributeValue.deleteMany({
      where: {
        attributeId: { in: validatedData.ids },
      },
    });

    // Then delete attributes
    const deletedAttributes = await tx.attribute.deleteMany({
      where: {
        id: { in: validatedData.ids },
      },
    });

    return deletedAttributes;
  });

  return NextResponse.json({
    message: `Đã xóa ${result.count} thuộc tính thành công`,
    deletedCount: result.count,
  });
}

async function handleBulkUpdate(body: any) {
  const validatedData = bulkUpdateSchema.parse(body);

  // Check if attributes exist
  const existingAttributes = await prisma.attribute.findMany({
    where: {
      id: { in: validatedData.ids },
    },
  });

  if (existingAttributes.length !== validatedData.ids.length) {
    return NextResponse.json(
      { error: "Một số thuộc tính không tồn tại" },
      { status: 404 }
    );
  }

  // Update attributes
  const updatedAttributes = await prisma.attribute.updateMany({
    where: {
      id: { in: validatedData.ids },
    },
    data: validatedData.data,
  });

  return NextResponse.json({
    message: `Đã cập nhật ${updatedAttributes.count} thuộc tính thành công`,
    updatedCount: updatedAttributes.count,
  });
}

async function handleBulkReorder(body: any) {
  const validatedData = bulkReorderSchema.parse(body);

  // Verify all attributes exist
  const attributeIds = validatedData.attributes.map((attr) => attr.id);
  const existingAttributes = await prisma.attribute.findMany({
    where: {
      id: { in: attributeIds },
    },
  });

  if (existingAttributes.length !== attributeIds.length) {
    return NextResponse.json(
      { error: "Một số thuộc tính không tồn tại" },
      { status: 404 }
    );
  }

  // Update sort orders in transaction
  await prisma.$transaction(
    validatedData.attributes.map((attr) =>
      prisma.attribute.update({
        where: { id: attr.id },
        data: { sortOrder: attr.sortOrder },
      })
    )
  );

  return NextResponse.json({
    message: "Cập nhật thứ tự thuộc tính thành công",
  });
}

// GET /api/admin/attributes/bulk - Get bulk operation status or statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Get bulk statistics
    const stats = await prisma.attribute.groupBy({
      by: ["type"],
      _count: {
        _all: true,
      },
    });

    const totalAttributes = await prisma.attribute.count();
    const attributesWithValues = await prisma.attribute.count({
      where: {
        values: {
          some: {},
        },
      },
    });
    const attributesInUse = await prisma.attribute.count({
      where: {
        products: {
          some: {},
        },
      },
    });

    return NextResponse.json({
      total: totalAttributes,
      withValues: attributesWithValues,
      inUse: attributesInUse,
      byType: stats.reduce((acc, stat) => {
        acc[stat.type] = stat._count._all;
        return acc;
      }, {} as Record<string, number>),
    });
  } catch (error) {
    console.error("Error fetching bulk statistics:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thống kê" },
      { status: 500 }
    );
  }
}
