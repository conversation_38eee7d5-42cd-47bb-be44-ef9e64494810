import { NextRequest } from "next/server";
import { PaginationParams, SearchParams } from "../types";

/**
 * Extract pagination parameters from request
 */
export function extractPaginationParams(
  request: NextRequest
): PaginationParams {
  const { searchParams } = new URL(request.url);

  const page = Math.max(1, parseInt(searchParams.get("page") || "1"));
  const limit = Math.min(
    100,
    Math.max(1, parseInt(searchParams.get("limit") || "20"))
  );
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

/**
 * Extract search and filter parameters from request
 */
export function extractSearchParams(request: NextRequest): SearchParams {
  const { searchParams } = new URL(request.url);
  const params: SearchParams = {};

  // Extract common search parameters
  const search = searchParams.get("search");
  if (search) {
    params.search = search;
  }

  const sortBy = searchParams.get("sortBy");
  if (sortBy) {
    params.sortBy = sortBy;
  }

  const sortOrder = searchParams.get("sortOrder");
  if (sortOrder && ["asc", "desc"].includes(sortOrder)) {
    params.sortOrder = sortOrder as "asc" | "desc";
  }

  // Extract all other parameters
  for (const [key, value] of searchParams.entries()) {
    if (!["page", "limit", "search", "sortBy", "sortOrder"].includes(key)) {
      params[key] = value;
    }
  }

  return params;
}

/**
 * Build Prisma where clause from search parameters
 */
export function buildWhereClause(
  searchParams: SearchParams,
  searchableFields: string[] = []
): any {
  const where: any = {};

  // Handle search across multiple fields
  if (searchParams.search && searchableFields.length > 0) {
    where.OR = searchableFields.map((field) => ({
      [field]: {
        contains: searchParams.search,
        mode: "insensitive",
      },
    }));
  }

  // Handle specific field filters
  Object.entries(searchParams).forEach(([key, value]) => {
    if (
      key !== "search" &&
      key !== "sortBy" &&
      key !== "sortOrder" &&
      key !== "page" &&
      key !== "limit" &&
      key !== "skip" &&
      value
    ) {
      // Handle different filter types
      if (key.endsWith("_gte")) {
        // Greater than or equal
        const field = key.replace("_gte", "");
        where[field] = { ...where[field], gte: parseValue(value) };
      } else if (key.endsWith("_lte")) {
        // Less than or equal
        const field = key.replace("_lte", "");
        where[field] = { ...where[field], lte: parseValue(value) };
      } else if (key.endsWith("_in")) {
        // In array
        const field = key.replace("_in", "");
        where[field] = { in: value.split(",") };
      } else if (key.endsWith("_not")) {
        // Not equal
        const field = key.replace("_not", "");
        where[field] = { not: parseValue(value) };
      } else if (key.endsWith("_contains")) {
        // Contains (case insensitive)
        const field = key.replace("_contains", "");
        where[field] = { contains: value, mode: "insensitive" };
      } else {
        // Exact match
        where[key] = parseValue(value);
      }
    }
  });

  return where;
}

/**
 * Build Prisma orderBy clause from search parameters
 */
export function buildOrderByClause(searchParams: SearchParams): any {
  if (!searchParams.sortBy) {
    return { createdAt: "desc" }; // Default sorting
  }

  return {
    [searchParams.sortBy]: searchParams.sortOrder || "asc",
  };
}

/**
 * Parse value to appropriate type
 */
function parseValue(value: string): any {
  // Try to parse as number
  if (!isNaN(Number(value))) {
    return Number(value);
  }

  // Try to parse as boolean
  if (value === "true") return true;
  if (value === "false") return false;

  // Try to parse as date
  if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }

  // Return as string
  return value;
}

/**
 * Calculate pagination metadata
 */
export function calculatePagination(
  total: number,
  page: number,
  limit: number
) {
  const pages = Math.ceil(total / limit);
  const hasNext = page < pages;
  const hasPrev = page > 1;

  return {
    page,
    limit,
    total,
    pages,
    hasNext,
    hasPrev,
  };
}

/**
 * Validate pagination parameters
 */
export function validatePaginationParams(params: PaginationParams): string[] {
  const errors: string[] = [];

  if (params.page < 1) {
    errors.push("Page phải lớn hơn 0");
  }

  if (params.limit < 1) {
    errors.push("Limit phải lớn hơn 0");
  }

  if (params.limit > 100) {
    errors.push("Limit không được vượt quá 100");
  }

  return errors;
}

/**
 * Create pagination helper for common use cases
 */
export class PaginationHelper {
  static extract(request: NextRequest) {
    return extractPaginationParams(request);
  }

  static extractSearch(request: NextRequest) {
    return extractSearchParams(request);
  }

  static buildWhere(
    searchParams: SearchParams,
    searchableFields: string[] = []
  ) {
    return buildWhereClause(searchParams, searchableFields);
  }

  static buildOrderBy(searchParams: SearchParams) {
    return buildOrderByClause(searchParams);
  }

  static calculate(total: number, page: number, limit: number) {
    return calculatePagination(total, page, limit);
  }

  static validate(params: PaginationParams) {
    return validatePaginationParams(params);
  }
}
