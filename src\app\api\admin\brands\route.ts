import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for brand creation/update
const brandSchema = z.object({
  name: z.string().min(1, "<PERSON><PERSON><PERSON> thương hiệu là bắt buộc"),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug là bắt buộc"),
  logo: z.string().optional(),
  logoType: z.enum(["INTERNAL", "EXTERNAL"]).default("INTERNAL"),
  externalLogoUrl: z.string().optional(),
  website: z
    .string()
    .url("Website phải là URL hợp lệ")
    .optional()
    .or(z.literal("")),
  isActive: z.boolean().default(true),
});

// GET /api/admin/brands - Get all brands with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const isActive = searchParams.get("isActive");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (isActive !== null && isActive !== undefined && isActive !== "") {
      where.isActive = isActive === "true";
    }

    // Get total count
    const total = await prisma.brand.count({ where });

    // Get brands with pagination
    const brands = await prisma.brand.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: brands,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Get brands error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách thương hiệu" },
      { status: 500 }
    );
  }
}

// POST /api/admin/brands - Create new brand
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = brandSchema.parse(body);

    // Check if slug already exists
    const existingBrand = await prisma.brand.findUnique({
      where: { slug: validatedData.slug },
    });

    if (existingBrand) {
      return NextResponse.json(
        { success: false, error: "Slug đã tồn tại" },
        { status: 400 }
      );
    }

    // Create brand
    const brand = await prisma.brand.create({
      data: validatedData,
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: brand,
      message: "Thương hiệu đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create brand error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo thương hiệu" },
      { status: 500 }
    );
  }
}
