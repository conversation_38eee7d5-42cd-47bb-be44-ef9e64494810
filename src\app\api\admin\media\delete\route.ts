import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin-auth';
import { deleteFile } from '@/lib/minio';

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const fileUrl = searchParams.get('url');

    if (!fileUrl) {
      return NextResponse.json(
        { success: false, error: 'URL file là bắt buộc' },
        { status: 400 }
      );
    }

    // Delete from MinIO
    const result = await deleteFile(fileUrl);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Xóa file thành công',
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Delete error:', error);
    return NextResponse.json(
      { success: false, error: 'Có lỗi xảy ra khi xóa file' },
      { status: 500 }
    );
  }
}
