import { prisma } from "@/lib/prisma";
import { 
  notificationRulesEngine,
  triggerLowStockAlert,
  triggerNewOrderNotification,
  triggerPaymentFailedAlert,
  triggerUserRegistrationNotification,
  triggerSystemErrorAlert,
  triggerOrderStatusChanged,
} from "@/lib/notification-rules";

/**
 * Notification Triggers - Automatic notification creation for system events
 */

// Product-related triggers
export async function onProductStockUpdate(
  productId: string,
  oldStock: number,
  newStock: number,
  productName: string
) {
  try {
    // Low stock alert
    if (newStock <= 10 && oldStock > 10) {
      await triggerLowStockAlert(productId, productName, newStock);
    }

    // Out of stock alert
    if (newStock === 0 && oldStock > 0) {
      await notificationRulesEngine.processEvent({
        type: "product.stock.empty",
        data: {
          productId,
          productName,
          previousStock: oldStock,
        },
        timestamp: new Date(),
      });
    }

    // Stock replenished notification
    if (newStock > 10 && oldStock <= 10) {
      await notificationRulesEngine.processEvent({
        type: "product.stock.replenished",
        data: {
          productId,
          productName,
          newStock,
          previousStock: oldStock,
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in product stock update trigger:", error);
  }
}

export async function onProductCreated(product: any, createdBy: string) {
  try {
    await notificationRulesEngine.processEvent({
      type: "product.created",
      data: {
        productId: product.id,
        productName: product.name,
        productSku: product.sku,
        categoryId: product.categoryId,
        price: product.price,
        stock: product.stock,
      },
      triggeredBy: createdBy,
      timestamp: new Date(),
    });
  } catch (error) {
    console.error("Error in product created trigger:", error);
  }
}

// Order-related triggers
export async function onOrderCreated(order: any) {
  try {
    await triggerNewOrderNotification(
      order.id,
      order.orderNumber,
      order.user?.name || order.guestName || "Guest",
      order.totalAmount
    );

    // High value order alert
    if (order.totalAmount >= 5000000) { // 5M VND
      await notificationRulesEngine.processEvent({
        type: "order.high_value",
        data: {
          orderId: order.id,
          orderNumber: order.orderNumber,
          customerName: order.user?.name || order.guestName || "Guest",
          totalAmount: order.totalAmount,
          itemCount: order.items?.length || 0,
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in order created trigger:", error);
  }
}

export async function onOrderStatusChanged(
  orderId: string,
  orderNumber: string,
  oldStatus: string,
  newStatus: string,
  updatedBy?: string
) {
  try {
    await triggerOrderStatusChanged(orderId, orderNumber, oldStatus, newStatus);

    // Special handling for important status changes
    const importantStatuses = ["CANCELLED", "REFUNDED", "RETURNED", "FAILED"];
    
    if (importantStatuses.includes(newStatus)) {
      await notificationRulesEngine.processEvent({
        type: "order.status.important_change",
        data: {
          orderId,
          orderNumber,
          oldStatus,
          newStatus,
          reason: getStatusChangeReason(newStatus),
        },
        triggeredBy: updatedBy,
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in order status changed trigger:", error);
  }
}

// Payment-related triggers
export async function onPaymentFailed(
  orderId: string,
  orderNumber: string,
  reason: string,
  amount: number
) {
  try {
    await triggerPaymentFailedAlert(orderId, orderNumber, reason);

    // Additional context for high-value payment failures
    if (amount >= 1000000) { // 1M VND
      await notificationRulesEngine.processEvent({
        type: "payment.high_value_failed",
        data: {
          orderId,
          orderNumber,
          amount,
          reason,
          severity: "high",
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in payment failed trigger:", error);
  }
}

export async function onPaymentSuccess(
  orderId: string,
  orderNumber: string,
  amount: number,
  paymentMethod: string
) {
  try {
    // Only notify for high-value successful payments
    if (amount >= 10000000) { // 10M VND
      await notificationRulesEngine.processEvent({
        type: "payment.high_value_success",
        data: {
          orderId,
          orderNumber,
          amount,
          paymentMethod,
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in payment success trigger:", error);
  }
}

// User-related triggers
export async function onUserRegistered(user: any) {
  try {
    await triggerUserRegistrationNotification(
      user.id,
      user.name,
      user.email
    );

    // Track registration source
    await notificationRulesEngine.processEvent({
      type: "user.registration.analytics",
      data: {
        userId: user.id,
        userName: user.name,
        userEmail: user.email,
        registrationSource: user.registrationSource || "direct",
        hasPhone: !!user.phone,
        hasAddress: !!user.address,
      },
      timestamp: new Date(),
    });
  } catch (error) {
    console.error("Error in user registered trigger:", error);
  }
}

export async function onUserSuspended(
  userId: string,
  userName: string,
  reason: string,
  suspendedBy: string
) {
  try {
    await notificationRulesEngine.processEvent({
      type: "user.suspended",
      data: {
        userId,
        userName,
        reason,
        suspendedBy,
      },
      triggeredBy: suspendedBy,
      timestamp: new Date(),
    });
  } catch (error) {
    console.error("Error in user suspended trigger:", error);
  }
}

// System-related triggers
export async function onSystemError(
  error: Error,
  location: string,
  severity: "low" | "medium" | "high" | "critical" = "medium",
  additionalData?: any
) {
  try {
    await triggerSystemErrorAlert(error.message, location);

    // Additional context for critical errors
    if (severity === "critical") {
      await notificationRulesEngine.processEvent({
        type: "system.critical_error",
        data: {
          errorMessage: error.message,
          errorStack: error.stack,
          location,
          severity,
          timestamp: new Date().toISOString(),
          additionalData,
        },
        timestamp: new Date(),
      });
    }
  } catch (triggerError) {
    console.error("Error in system error trigger:", triggerError);
    // Don't throw here to avoid infinite loops
  }
}

export async function onDatabaseConnectionLost() {
  try {
    await notificationRulesEngine.processEvent({
      type: "system.database.connection_lost",
      data: {
        timestamp: new Date().toISOString(),
        severity: "critical",
      },
      timestamp: new Date(),
    });
  } catch (error) {
    console.error("Error in database connection lost trigger:", error);
  }
}

export async function onHighServerLoad(cpuUsage: number, memoryUsage: number) {
  try {
    if (cpuUsage > 80 || memoryUsage > 80) {
      await notificationRulesEngine.processEvent({
        type: "system.high_load",
        data: {
          cpuUsage,
          memoryUsage,
          severity: cpuUsage > 90 || memoryUsage > 90 ? "critical" : "high",
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in high server load trigger:", error);
  }
}

// Admin-related triggers
export async function onAdminLogin(adminId: string, adminName: string, ipAddress: string) {
  try {
    // Only notify for suspicious login patterns
    const recentLogins = await prisma.auditLog.count({
      where: {
        adminId,
        action: "LOGIN",
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });

    if (recentLogins > 10) { // More than 10 logins in 24h
      await notificationRulesEngine.processEvent({
        type: "admin.suspicious_login",
        data: {
          adminId,
          adminName,
          ipAddress,
          recentLoginCount: recentLogins,
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in admin login trigger:", error);
  }
}

export async function onAdminPermissionChanged(
  targetAdminId: string,
  targetAdminName: string,
  changedBy: string,
  oldPermissions: any,
  newPermissions: any
) {
  try {
    await notificationRulesEngine.processEvent({
      type: "admin.permission_changed",
      data: {
        targetAdminId,
        targetAdminName,
        changedBy,
        oldPermissions,
        newPermissions,
        permissionDiff: getPermissionDiff(oldPermissions, newPermissions),
      },
      triggeredBy: changedBy,
      timestamp: new Date(),
    });
  } catch (error) {
    console.error("Error in admin permission changed trigger:", error);
  }
}

// Inventory-related triggers
export async function onInventoryAlert(
  productId: string,
  productName: string,
  currentStock: number,
  reorderLevel: number
) {
  try {
    if (currentStock <= reorderLevel) {
      await notificationRulesEngine.processEvent({
        type: "inventory.reorder_needed",
        data: {
          productId,
          productName,
          currentStock,
          reorderLevel,
          urgency: currentStock === 0 ? "critical" : "high",
        },
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in inventory alert trigger:", error);
  }
}

// Helper functions
function getStatusChangeReason(status: string): string {
  const reasons = {
    CANCELLED: "Order was cancelled",
    REFUNDED: "Payment was refunded",
    RETURNED: "Product was returned",
    FAILED: "Order processing failed",
  };
  return reasons[status as keyof typeof reasons] || "Status changed";
}

function getPermissionDiff(oldPermissions: any, newPermissions: any): any {
  const diff = {
    added: [] as string[],
    removed: [] as string[],
    modified: [] as string[],
  };

  // Simple diff implementation
  const oldKeys = Object.keys(oldPermissions || {});
  const newKeys = Object.keys(newPermissions || {});

  for (const key of newKeys) {
    if (!oldKeys.includes(key)) {
      diff.added.push(key);
    } else if (oldPermissions[key] !== newPermissions[key]) {
      diff.modified.push(key);
    }
  }

  for (const key of oldKeys) {
    if (!newKeys.includes(key)) {
      diff.removed.push(key);
    }
  }

  return diff;
}

// Batch trigger for multiple events
export async function triggerBatchNotifications(events: Array<{
  type: string;
  data: any;
  triggeredBy?: string;
}>) {
  try {
    for (const event of events) {
      await notificationRulesEngine.processEvent({
        ...event,
        timestamp: new Date(),
      });
    }
  } catch (error) {
    console.error("Error in batch notification trigger:", error);
  }
}

// Initialize notification triggers
export async function initializeNotificationTriggers() {
  try {
    await notificationRulesEngine.initialize();
    console.log("Notification triggers initialized successfully");
  } catch (error) {
    console.error("Failed to initialize notification triggers:", error);
  }
}
