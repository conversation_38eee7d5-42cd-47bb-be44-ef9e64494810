export type AttributeType =
  | "TEXT"
  | "NUMBER"
  | "COLOR"
  | "SIZE"
  | "BOOLEAN"
  | "SELECT"
  | "MULTI_SELECT";

export interface Attribute {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: AttributeType;
  isRequired: boolean;
  isFilterable: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  values?: AttributeValue[];
  _count?: {
    values: number;
    products: number;
  };
}

export interface AttributeValue {
  id: string;
  attributeId: string;
  value: string;
  slug: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  attribute?: Attribute;
  _count?: {
    products: number;
  };
}

export interface ProductAttribute {
  id: string;
  productId: string;
  attributeId: string;
  attributeValueId: string;
  createdAt: string;
  product?: {
    id: string;
    name: string;
    sku: string;
  };
  attribute?: Attribute;
  attributeValue?: AttributeValue;
}

export interface AttributeFormData {
  name: string;
  slug?: string;
  description?: string;
  type: AttributeType;
  isRequired?: boolean;
  isFilterable?: boolean;
  sortOrder?: number;
  values?: AttributeValueFormData[];
}

export interface AttributeValueFormData {
  id?: string;
  value: string;
  slug?: string;
  sortOrder?: number;
}

export interface AttributeFilters {
  search?: string;
  type?: AttributeType;
  isRequired?: boolean;
  isFilterable?: boolean;
  page?: number;
  limit?: number;
  sortBy?: "name" | "type" | "sortOrder" | "createdAt";
  sortOrder?: "asc" | "desc";
}

export interface AttributeStats {
  total: number;
  byType: Record<AttributeType, number>;
  withValues: number;
  withoutValues: number;
  required: number;
  filterable: number;
}

export const ATTRIBUTE_TYPE_LABELS: Record<AttributeType, string> = {
  TEXT: "Văn bản",
  NUMBER: "Số",
  COLOR: "Màu sắc",
  SIZE: "Kích thước",
  BOOLEAN: "Có/Không",
  SELECT: "Lựa chọn đơn",
  MULTI_SELECT: "Lựa chọn nhiều",
};

export const ATTRIBUTE_TYPE_DESCRIPTIONS: Record<AttributeType, string> = {
  TEXT: "Nhập văn bản tự do",
  NUMBER: "Nhập số (ví dụ: trọng lượng, kích thước)",
  COLOR: "Chọn màu sắc",
  SIZE: "Chọn kích thước (S, M, L, XL...)",
  BOOLEAN: "Lựa chọn có/không",
  SELECT: "Chọn một giá trị từ danh sách",
  MULTI_SELECT: "Chọn nhiều giá trị từ danh sách",
};
