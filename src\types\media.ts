export type MediaType = 'INTERNAL' | 'EXTERNAL';

export interface MediaField {
  url?: string;
  type: MediaType;
  externalUrl?: string;
}

export interface MediaFieldFormData {
  url?: string;
  type: MediaType;
  externalUrl?: string;
}

// Helper functions for media handling
export const createMediaField = (url?: string, type: MediaType = 'INTERNAL', externalUrl?: string): MediaField => ({
  url,
  type,
  externalUrl,
});

export const getMediaUrl = (field: MediaField): string | undefined => {
  if (field.type === 'EXTERNAL') {
    return field.externalUrl;
  }
  return field.url;
};

export const isValidMediaUrl = (url: string, type: MediaType): boolean => {
  if (!url) return false;
  
  if (type === 'EXTERNAL') {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
  
  // For internal media, just check if it's a non-empty string
  return url.length > 0;
};
