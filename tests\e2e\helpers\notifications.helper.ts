/**
 * Notifications E2E Test Helper
 * Helper functions cho E2E testing của notifications
 */

import { Page, expect } from "@playwright/test";

export interface NotificationData {
  title: string;
  message: string;
  type?: "INFO" | "SUCCESS" | "WARNING" | "ERROR" | "SYSTEM";
  priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_ADMIN" | "ROLE_MODERATOR";
  targetId?: string;
  actionUrl?: string;
  expiresAt?: string;
}

export class NotificationsHelper {
  constructor(private page: Page) {}

  /**
   * Navigate to notifications page
   */
  async goto() {
    await this.page.goto("/admin/notifications");
    await this.waitForPageLoad();
  }

  /**
   * Navigate to notification settings page
   */
  async gotoSettings() {
    await this.page.goto("/admin/notifications/settings");
    await this.waitForPageLoad();
  }

  /**
   * Wait for notifications page to load
   */
  async waitForPageLoad() {
    await this.page.waitForSelector('[data-testid="notifications-page"]', { timeout: 10000 });
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Click notification bell icon
   */
  async clickNotificationBell() {
    await this.page.click('[data-testid="notification-bell"]');
    await this.page.waitForSelector('[data-testid="notification-dropdown"]');
  }

  /**
   * Get notification bell badge count
   */
  async getBellBadgeCount(): Promise<number> {
    const badge = this.page.locator('[data-testid="notification-badge"]');
    const isVisible = await badge.isVisible();
    if (!isVisible) return 0;
    
    const text = await badge.textContent();
    return parseInt(text || "0");
  }

  /**
   * Get notifications from dropdown
   */
  async getDropdownNotifications() {
    return this.page.locator('[data-testid="dropdown-notification-item"]');
  }

  /**
   * Get notifications from management page
   */
  async getNotificationRows() {
    return this.page.locator('[data-testid="notification-row"]');
  }

  /**
   * Get notifications count
   */
  async getNotificationsCount(): Promise<number> {
    const rows = await this.getNotificationRows();
    return await rows.count();
  }

  /**
   * Create new notification
   */
  async createNotification(data: NotificationData) {
    await this.page.click('[data-testid="create-notification-btn"]');
    await this.page.waitForSelector('[data-testid="notification-form"]');

    // Fill form
    await this.page.fill('[data-testid="title-input"]', data.title);
    await this.page.fill('[data-testid="message-input"]', data.message);

    if (data.type) {
      await this.page.selectOption('[data-testid="type-select"]', data.type);
    }

    if (data.priority) {
      await this.page.selectOption('[data-testid="priority-select"]', data.priority);
    }

    if (data.targetType) {
      await this.page.selectOption('[data-testid="target-type-select"]', data.targetType);
    }

    if (data.targetId) {
      await this.page.fill('[data-testid="target-id-input"]', data.targetId);
    }

    if (data.actionUrl) {
      await this.page.fill('[data-testid="action-url-input"]', data.actionUrl);
    }

    if (data.expiresAt) {
      await this.page.fill('[data-testid="expires-at-input"]', data.expiresAt);
    }

    // Submit form
    await this.page.click('[data-testid="submit-notification-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Mark notification as read
   */
  async markAsRead(index: number = 0) {
    const rows = await this.getNotificationRows();
    await rows.nth(index).locator('[data-testid="mark-read-btn"]').click();
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Mark notification as unread
   */
  async markAsUnread(index: number = 0) {
    const rows = await this.getNotificationRows();
    await rows.nth(index).locator('[data-testid="mark-unread-btn"]').click();
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Delete notification
   */
  async deleteNotification(index: number = 0) {
    const rows = await this.getNotificationRows();
    await rows.nth(index).locator('[data-testid="delete-btn"]').click();
    
    // Confirm deletion
    await this.page.click('[data-testid="confirm-delete-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Bulk mark as read
   */
  async bulkMarkAsRead() {
    await this.page.click('[data-testid="select-all-checkbox"]');
    await this.page.click('[data-testid="bulk-mark-read-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Bulk delete notifications
   */
  async bulkDelete() {
    await this.page.click('[data-testid="select-all-checkbox"]');
    await this.page.click('[data-testid="bulk-delete-btn"]');
    await this.page.click('[data-testid="confirm-bulk-delete-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Filter notifications by type
   */
  async filterByType(type: string) {
    await this.page.selectOption('[data-testid="type-filter"]', type);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Filter notifications by priority
   */
  async filterByPriority(priority: string) {
    await this.page.selectOption('[data-testid="priority-filter"]', priority);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Filter notifications by read status
   */
  async filterByReadStatus(status: "all" | "read" | "unread") {
    await this.page.selectOption('[data-testid="read-status-filter"]', status);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Search notifications
   */
  async search(query: string) {
    await this.page.fill('[data-testid="search-input"]', query);
    await this.page.press('[data-testid="search-input"]', "Enter");
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Clear all filters
   */
  async clearFilters() {
    await this.page.click('[data-testid="clear-filters-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Click on notification to view details or navigate
   */
  async clickNotification(index: number = 0) {
    const rows = await this.getNotificationRows();
    await rows.nth(index).click();
  }

  /**
   * Click notification in dropdown
   */
  async clickDropdownNotification(index: number = 0) {
    const notifications = await this.getDropdownNotifications();
    await notifications.nth(index).click();
  }

  /**
   * Verify notification exists
   */
  async notificationExists(title: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(`text=${title}`, { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify notification data in table
   */
  async verifyNotificationInTable(data: NotificationData, rowIndex: number = 0) {
    const rows = await this.getNotificationRows();
    const row = rows.nth(rowIndex);

    await expect(row.locator('[data-testid="notification-title"]')).toContainText(data.title);
    await expect(row.locator('[data-testid="notification-message"]')).toContainText(data.message);

    if (data.type) {
      await expect(row.locator('[data-testid="notification-type"]')).toContainText(data.type);
    }
    if (data.priority) {
      await expect(row.locator('[data-testid="notification-priority"]')).toContainText(data.priority);
    }
  }

  /**
   * Verify notification in dropdown
   */
  async verifyNotificationInDropdown(data: NotificationData, index: number = 0) {
    const notifications = await this.getDropdownNotifications();
    const notification = notifications.nth(index);

    await expect(notification.locator('[data-testid="dropdown-title"]')).toContainText(data.title);
    await expect(notification.locator('[data-testid="dropdown-message"]')).toContainText(data.message);
  }

  /**
   * Wait for real-time notification to appear
   */
  async waitForRealtimeNotification(title: string, timeout: number = 10000) {
    await this.page.waitForSelector(`text=${title}`, { timeout });
  }

  /**
   * Test email notification settings
   */
  async testEmailNotification() {
    await this.page.click('[data-testid="test-email-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(preferences: {
    emailEnabled?: boolean;
    quietHoursEnabled?: boolean;
    quietHoursStart?: string;
    quietHoursEnd?: string;
    notificationTypes?: string[];
  }) {
    if (preferences.emailEnabled !== undefined) {
      const checkbox = this.page.locator('[data-testid="email-enabled-checkbox"]');
      const isChecked = await checkbox.isChecked();
      if (isChecked !== preferences.emailEnabled) {
        await checkbox.click();
      }
    }

    if (preferences.quietHoursEnabled !== undefined) {
      const checkbox = this.page.locator('[data-testid="quiet-hours-enabled-checkbox"]');
      const isChecked = await checkbox.isChecked();
      if (isChecked !== preferences.quietHoursEnabled) {
        await checkbox.click();
      }
    }

    if (preferences.quietHoursStart) {
      await this.page.fill('[data-testid="quiet-hours-start"]', preferences.quietHoursStart);
    }

    if (preferences.quietHoursEnd) {
      await this.page.fill('[data-testid="quiet-hours-end"]', preferences.quietHoursEnd);
    }

    if (preferences.notificationTypes) {
      for (const type of preferences.notificationTypes) {
        await this.page.check(`[data-testid="notification-type-${type}"]`);
      }
    }

    await this.page.click('[data-testid="save-preferences-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Refresh notifications
   */
  async refresh() {
    await this.page.click('[data-testid="refresh-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Go to next page
   */
  async goToNextPage() {
    await this.page.click('[data-testid="next-page-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage() {
    await this.page.click('[data-testid="prev-page-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Verify success toast
   */
  async expectSuccessToast(message?: string) {
    const toast = this.page.locator('[data-testid="success-toast"]');
    await expect(toast).toBeVisible();
    if (message) {
      await expect(toast).toContainText(message);
    }
  }

  /**
   * Verify error toast
   */
  async expectErrorToast(message?: string) {
    const toast = this.page.locator('[data-testid="error-toast"]');
    await expect(toast).toBeVisible();
    if (message) {
      await expect(toast).toContainText(message);
    }
  }

  /**
   * Verify empty state
   */
  async expectEmptyState() {
    await expect(this.page.locator('[data-testid="empty-state"]')).toBeVisible();
  }

  /**
   * Verify loading state
   */
  async expectLoadingState() {
    await expect(this.page.locator('[data-testid="loading-spinner"]')).toBeVisible();
  }
}
