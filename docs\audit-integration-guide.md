# Audit Logging Integration Guide

Hướng dẫn tích hợp audit logging vào các admin operations trong NS Shop.

## Tổng quan

Audit logging system đã được triển khai để theo dõi tất cả hoạt động của admin trong hệ thống. Hệ thống bao gồm:

- **Audit Middleware**: Tự động log các operations
- **Manual Logging**: Log thủ công cho các operations phức tạp
- **Notification Rules**: Tự động tạo thông báo dựa trên audit events

## 1. Sử dụng Audit Middleware

### Cách sử dụng cơ bản

```typescript
import { withAudit, auditConfigs } from "@/lib/audit-middleware";

// Sử dụng config có sẵn
const auditedHandler = withAudit("UPDATE", "Product", auditConfigs.updateProduct)(
  async (request: NextRequest, context: any) => {
    // Your handler logic here
  }
);

export { auditedHandler as PUT };
```

### Tạo custom audit config

```typescript
const customAuditConfig = withAudit("CREATE", "CustomResource", {
  getResourceId: (request, params, body) => body?.id,
  getOldValues: async (request, params, body) => {
    // Fetch old values if needed
    return await prisma.customResource.findUnique({ where: { id: params.id } });
  },
  getNewValues: (request, params, body) => body,
  getDescription: (request, params, body) => `Created custom resource: ${body?.name}`,
  skipLogging: (request, params, body) => {
    // Skip logging for certain conditions
    return body?.skipAudit === true;
  },
});
```

## 2. Manual Audit Logging

Cho các operations phức tạp hoặc batch operations:

```typescript
import { logAdminAction, getRequestMetadata } from "@/lib/audit-logger";

export async function POST(request: NextRequest) {
  const session = await getServerSession(adminAuthOptions);
  const { ipAddress, userAgent } = getRequestMetadata(request);

  // Your operation logic here
  const result = await performComplexOperation();

  // Manual audit logging
  await logAdminAction({
    action: "COMPLEX_OPERATION",
    resource: "CustomResource",
    resourceId: result.id,
    oldValues: oldData,
    newValues: newData,
    description: "Performed complex operation with custom logic",
    adminId: session.user.id,
    ipAddress,
    userAgent,
  });

  return NextResponse.json({ success: true });
}
```

## 3. Notification Rules Integration

### Trigger notifications từ operations

```typescript
import { 
  triggerLowStockAlert,
  triggerNewOrderNotification,
  triggerPaymentFailedAlert 
} from "@/lib/notification-rules";

// Trong product update
if (updatedProduct.stock <= 10) {
  await triggerLowStockAlert(
    updatedProduct.id,
    updatedProduct.name,
    updatedProduct.stock
  );
}

// Trong order creation
await triggerNewOrderNotification(
  order.id,
  order.orderNumber,
  order.user.name,
  order.totalAmount
);
```

### Custom notification events

```typescript
import { notificationRulesEngine } from "@/lib/notification-rules";

await notificationRulesEngine.processEvent({
  type: "custom.event.type",
  data: {
    customField1: "value1",
    customField2: "value2",
  },
  triggeredBy: session.user.id,
  timestamp: new Date(),
});
```

## 4. Các Operations cần tích hợp

### Products
- ✅ CREATE: Tạo sản phẩm mới
- ✅ UPDATE: Cập nhật thông tin sản phẩm
- ✅ DELETE: Xóa sản phẩm
- ✅ BULK_UPDATE: Cập nhật hàng loạt
- ✅ STOCK_UPDATE: Cập nhật kho hàng

### Categories
- ⏳ CREATE: Tạo danh mục mới
- ⏳ UPDATE: Cập nhật danh mục
- ⏳ DELETE: Xóa danh mục
- ⏳ REORDER: Sắp xếp lại danh mục

### Orders
- ⏳ STATUS_UPDATE: Cập nhật trạng thái đơn hàng
- ⏳ PAYMENT_UPDATE: Cập nhật thanh toán
- ⏳ CANCEL: Hủy đơn hàng
- ⏳ REFUND: Hoàn tiền

### Users
- ⏳ CREATE: Tạo người dùng
- ⏳ UPDATE: Cập nhật thông tin
- ⏳ SUSPEND: Tạm khóa tài khoản
- ⏳ DELETE: Xóa tài khoản

### Settings
- ⏳ UPDATE: Cập nhật cài đặt hệ thống
- ⏳ BACKUP: Sao lưu dữ liệu
- ⏳ RESTORE: Khôi phục dữ liệu

### Admin Users
- ✅ CREATE: Tạo admin mới
- ✅ UPDATE: Cập nhật thông tin admin
- ✅ DELETE: Xóa admin
- ✅ PERMISSION_UPDATE: Cập nhật quyền

## 5. Best Practices

### 1. Sensitive Data Handling
```typescript
// Loại bỏ sensitive data khỏi audit logs
const sanitizeData = (data: any) => {
  const { password, creditCard, ...safeData } = data;
  return safeData;
};

await logAdminAction({
  // ...
  newValues: sanitizeData(body),
});
```

### 2. Performance Considerations
```typescript
// Skip logging cho read operations không quan trọng
const skipLogging = (request, params, body) => {
  // Skip cho GET requests của public data
  return request.method === "GET" && !params.sensitive;
};
```

### 3. Error Handling
```typescript
try {
  const result = await performOperation();
  await logAdminAction({
    action: "SUCCESS_OPERATION",
    // ...
  });
} catch (error) {
  await logAdminAction({
    action: "FAILED_OPERATION",
    description: `Operation failed: ${error.message}`,
    // ...
  });
  throw error;
}
```

### 4. Batch Operations
```typescript
// Log từng item trong batch operation
for (const item of items) {
  await logAdminAction({
    action: "BATCH_ITEM_UPDATE",
    resourceId: item.id,
    description: `Updated item ${item.id} in batch operation`,
    // ...
  });
}

// Hoặc log tổng quan
await logAdminAction({
  action: "BATCH_OPERATION",
  description: `Updated ${items.length} items`,
  metadata: { itemIds: items.map(i => i.id) },
  // ...
});
```

## 6. Testing Audit Logs

### Unit Tests
```typescript
import { logAdminAction } from "@/lib/audit-logger";

jest.mock("@/lib/audit-logger");

test("should log admin action", async () => {
  await performOperation();
  
  expect(logAdminAction).toHaveBeenCalledWith({
    action: "UPDATE",
    resource: "Product",
    // ...
  });
});
```

### Integration Tests
```typescript
test("should create audit log in database", async () => {
  const response = await request(app)
    .put("/api/admin/products/123")
    .send(updateData);

  const auditLog = await prisma.auditLog.findFirst({
    where: {
      action: "UPDATE",
      resource: "Product",
      resourceId: "123",
    },
  });

  expect(auditLog).toBeTruthy();
});
```

## 7. Monitoring và Alerting

### Dashboard Metrics
- Số lượng operations theo ngày/tuần/tháng
- Top admins có nhiều hoạt động nhất
- Các operations thất bại
- Thời gian response của operations

### Alerts
- Quá nhiều failed operations
- Unusual activity patterns
- Sensitive data access
- Bulk operations lớn

## 8. Compliance và Security

### Data Retention
- Audit logs được lưu trữ tối thiểu 1 năm
- Tự động archive logs cũ
- Backup audit logs định kỳ

### Access Control
- Chỉ ADMIN mới có thể xem audit logs
- Audit logs không thể bị sửa đổi
- Encrypted sensitive data trong logs

### Privacy
- Không log password hoặc sensitive data
- Anonymize user data khi cần thiết
- Tuân thủ GDPR và các quy định về privacy
