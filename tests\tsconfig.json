{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["../src/*"], "@/components/*": ["../src/components/*"], "@/lib/*": ["../src/lib/*"], "@/hooks/*": ["../src/hooks/*"], "@/types/*": ["../src/types/*"], "@/contexts/*": ["../src/contexts/*"], "@/providers/*": ["../src/providers/*"]}, "types": ["jest", "@testing-library/jest-dom", "node"]}, "include": ["**/*.ts", "**/*.tsx", "../src/**/*.ts", "../src/**/*.tsx", "../next-env.d.ts", "jest-dom.d.ts"], "exclude": ["../node_modules", "../.next", "../out"]}