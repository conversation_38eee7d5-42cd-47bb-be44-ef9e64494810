"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Plus,
  CreditCard,
  Smartphone,
  Building2,
  DollarSign,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  <PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";

interface PaymentGateway {
  id: string;
  name: string;
  provider:
    | "VNPAY"
    | "MOMO"
    | "ZALOPAY"
    | "PAYPAL"
    | "STRIPE"
    | "BANK_TRANSFER"
    | "COD"
    | "CUSTOM";
  isActive: boolean;
  isDefault: boolean;
  config: any;
  credentials: any;
  supportedMethods: string[];
  fees?: any;
  limits?: any;
  createdAt: string;
  updatedAt: string;
  transactions: PaymentTransaction[];
}

interface PaymentTransaction {
  id: string;
  orderId?: string;
  gatewayId: string;
  externalId?: string;
  amount: number;
  currency: string;
  method: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  status:
    | "PENDING"
    | "PROCESSING"
    | "SUCCESS"
    | "FAILED"
    | "CANCELLED"
    | "REFUNDED"
    | "PARTIAL_REFUND";
  gatewayResponse?: any;
  failureReason?: string;
  processedAt?: string;
  refundedAt?: string;
  refundAmount?: number;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  gateway: PaymentGateway;
  order?: {
    id: string;
    total: number;
    user: {
      name: string;
      email: string;
    };
  };
}

const paymentProviders = [
  {
    value: "VNPAY",
    label: "VNPay",
    icon: CreditCard,
    color: "bg-blue-100 text-blue-800",
  },
  {
    value: "MOMO",
    label: "MoMo",
    icon: Smartphone,
    color: "bg-pink-100 text-pink-800",
  },
  {
    value: "ZALOPAY",
    label: "ZaloPay",
    icon: Smartphone,
    color: "bg-blue-100 text-blue-800",
  },
  {
    value: "PAYPAL",
    label: "PayPal",
    icon: CreditCard,
    color: "bg-blue-100 text-blue-800",
  },
  {
    value: "STRIPE",
    label: "Stripe",
    icon: CreditCard,
    color: "bg-purple-100 text-purple-800",
  },
  {
    value: "BANK_TRANSFER",
    label: "Chuyển khoản",
    icon: Building2,
    color: "bg-green-100 text-green-800",
  },
  {
    value: "COD",
    label: "Thanh toán khi nhận hàng",
    icon: DollarSign,
    color: "bg-orange-100 text-orange-800",
  },
  {
    value: "CUSTOM",
    label: "Tùy chỉnh",
    icon: Settings,
    color: "bg-gray-100 text-gray-800",
  },
];

const transactionStatuses = [
  {
    value: "PENDING",
    label: "Chờ xử lý",
    color: "bg-yellow-100 text-yellow-800",
    icon: Clock,
  },
  {
    value: "PROCESSING",
    label: "Đang xử lý",
    color: "bg-blue-100 text-blue-800",
    icon: RefreshCw,
  },
  {
    value: "SUCCESS",
    label: "Thành công",
    color: "bg-green-100 text-green-800",
    icon: CheckCircle,
  },
  {
    value: "FAILED",
    label: "Thất bại",
    color: "bg-red-100 text-red-800",
    icon: XCircle,
  },
  {
    value: "CANCELLED",
    label: "Đã hủy",
    color: "bg-gray-100 text-gray-800",
    icon: XCircle,
  },
  {
    value: "REFUNDED",
    label: "Đã hoàn tiền",
    color: "bg-purple-100 text-purple-800",
    icon: TrendingDown,
  },
  {
    value: "PARTIAL_REFUND",
    label: "Hoàn tiền một phần",
    color: "bg-orange-100 text-orange-800",
    icon: TrendingDown,
  },
];

export default function AdminPaymentsPage() {
  const [activeTab, setActiveTab] = useState("gateways");
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(
    null
  );
  const [isCreateGatewayOpen, setIsCreateGatewayOpen] = useState(false);
  const [editingGateway, setEditingGateway] = useState<PaymentGateway | null>(
    null
  );

  // Data fetching for payment gateways
  const {
    data: gateways,
    loading: gatewaysLoading,
    refresh: refreshGateways,
    setParams: setGatewayParams,
    params: gatewayParams,
  } = useAdminData<PaymentGateway>({
    endpoint: "/api/admin/payment-gateways",
    initialParams: { page: 1, limit: 20 },
  });

  // Data fetching for payment transactions
  const {
    data: transactions,
    loading: transactionsLoading,
    refresh: refreshTransactions,
    setParams: setTransactionParams,
    params: transactionParams,
  } = useAdminData<PaymentTransaction>({
    endpoint: "/api/admin/payment-transactions",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createGateway,
    update: updateGateway,
    remove: deleteGateway,
  } = useAdminCrud("/api/admin/payment-gateways");

  // Gateway table configuration
  const gatewayTableConfig = {
    columns: [
      {
        key: "name",
        title: "Tên Gateway",
        sortable: true,
        render: (gateway: PaymentGateway) => (
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {(() => {
                const provider = paymentProviders.find(
                  (p) => p.value === gateway?.provider
                );
                const Icon = provider?.icon || CreditCard;
                return <Icon className="h-8 w-8 text-gray-600" />;
              })()}
            </div>
            <div>
              <div className="font-medium">{gateway?.name || "N/A"}</div>
              <div className="text-sm text-muted-foreground">
                {paymentProviders.find((p) => p.value === gateway?.provider)
                  ?.label || ""}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "provider",
        title: "Nhà cung cấp",
        render: (gateway: PaymentGateway) => {
          const provider = paymentProviders.find(
            (p) => p.value === gateway?.provider
          );
          return (
            <Badge className={provider?.color}>
              {provider?.label || gateway?.provider || "N/A"}
            </Badge>
          );
        },
      },
      {
        key: "supportedMethods",
        title: "Phương thức",
        render: (gateway: PaymentGateway) => (
          <div className="flex flex-wrap gap-1">
            {gateway?.supportedMethods?.slice(0, 2).map((method) => (
              <Badge key={method} variant="outline" className="text-xs">
                {method}
              </Badge>
            )) || []}
            {(gateway?.supportedMethods?.length || 0) > 2 && (
              <Badge variant="outline" className="text-xs">
                +{(gateway?.supportedMethods?.length || 0) - 2}
              </Badge>
            )}
          </div>
        ),
      },
      {
        key: "status",
        title: "Trạng thái",
        render: (gateway: PaymentGateway) => (
          <div className="flex items-center space-x-2">
            <Badge variant={gateway?.isActive ? "default" : "secondary"}>
              {gateway?.isActive ? "Hoạt động" : "Tạm dừng"}
            </Badge>
            {gateway?.isDefault && (
              <Badge variant="outline" className="text-xs">
                Mặc định
              </Badge>
            )}
          </div>
        ),
      },
      {
        key: "transactions",
        title: "Giao dịch",
        render: (gateway: PaymentGateway) => (
          <div className="text-sm">
            <div className="font-medium">
              {gateway.transactions?.length || 0}
            </div>
            <div className="text-muted-foreground">giao dịch</div>
          </div>
        ),
      },
      {
        key: "createdAt",
        title: "Ngày tạo",
        sortable: true,
        render: (gateway: PaymentGateway) =>
          gateway?.createdAt
            ? new Date(gateway.createdAt).toLocaleDateString("vi-VN")
            : "N/A",
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: (gateway: PaymentGateway) => setSelectedGateway(gateway),
        },
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: (gateway: PaymentGateway) => setEditingGateway(gateway),
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          onClick: (gateway: PaymentGateway) => handleDeleteGateway(gateway),
          variant: "destructive" as const,
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  // Transaction table configuration
  const transactionTableConfig = {
    columns: [
      {
        key: "id",
        title: "ID Giao dịch",
        render: (transaction: PaymentTransaction) => (
          <div className="font-mono text-sm">
            #{transaction?.id?.slice(-8).toUpperCase() || "N/A"}
          </div>
        ),
      },
      {
        key: "order",
        title: "Đơn hàng",
        render: (transaction: PaymentTransaction) => (
          <div>
            {transaction?.order ? (
              <div>
                <div className="font-medium">
                  #{transaction?.orderId?.slice(-8).toUpperCase() || "N/A"}
                </div>
                <div className="text-sm text-muted-foreground">
                  {transaction?.order?.user?.name || "N/A"}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground">Không có</span>
            )}
          </div>
        ),
      },
      {
        key: "amount",
        title: "Số tiền",
        sortable: true,
        render: (transaction: PaymentTransaction) => (
          <div className="font-medium">
            {transaction?.amount?.toLocaleString() || "0"}{" "}
            {transaction?.currency || "VND"}
          </div>
        ),
      },
      {
        key: "method",
        title: "Phương thức",
        render: (transaction: PaymentTransaction) => (
          <Badge variant="outline">{transaction?.method || "N/A"}</Badge>
        ),
      },
      {
        key: "status",
        title: "Trạng thái",
        render: (transaction: PaymentTransaction) => {
          const status = transactionStatuses.find(
            (s) => s.value === transaction?.status
          );
          const Icon = status?.icon || Clock;
          return (
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <Badge className={status?.color}>
                {status?.label || transaction?.status || "N/A"}
              </Badge>
            </div>
          );
        },
      },
      {
        key: "gateway",
        title: "Gateway",
        render: (transaction: PaymentTransaction) => (
          <div className="text-sm">{transaction?.gateway?.name || "N/A"}</div>
        ),
      },
      {
        key: "createdAt",
        title: "Ngày tạo",
        sortable: true,
        render: (transaction: PaymentTransaction) => (
          <div className="text-sm">
            {transaction?.createdAt
              ? new Date(transaction.createdAt).toLocaleDateString("vi-VN")
              : "N/A"}
          </div>
        ),
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: (transaction: PaymentTransaction) => {
            // Handle view transaction details
          },
        },
      ],
    },
  };

  const handleDeleteGateway = async (gateway: PaymentGateway) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa gateway "${gateway.name}"?`)) {
      return;
    }

    const result = await deleteGateway(gateway.id);
    if (result) {
      toast.success("Xóa gateway thành công");
      refreshGateways();
    }
  };

  // Calculate stats
  const gatewayStats = {
    total: gateways?.length || 0,
    active: gateways?.filter((g) => g.isActive).length || 0,
    inactive: gateways?.filter((g) => !g.isActive).length || 0,
  };

  const transactionStats = {
    total: transactions?.length || 0,
    success: transactions?.filter((t) => t.status === "SUCCESS").length || 0,
    pending: transactions?.filter((t) => t.status === "PENDING").length || 0,
    failed: transactions?.filter((t) => t.status === "FAILED").length || 0,
    totalAmount:
      transactions
        ?.filter((t) => t.status === "SUCCESS")
        .reduce((sum, t) => sum + t.amount, 0) || 0,
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý Thanh toán</h1>
          <p className="text-muted-foreground">
            Quản lý phương thức thanh toán, gateway và theo dõi giao dịch
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateGatewayOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Thêm Gateway
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="gateways">Payment Gateways</TabsTrigger>
          <TabsTrigger value="transactions">Giao dịch</TabsTrigger>
          <TabsTrigger value="analytics">Báo cáo</TabsTrigger>
        </TabsList>

        <TabsContent value="gateways" className="space-y-6">
          {/* Gateway Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CreditCard className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Tổng Gateway
                    </p>
                    <p className="text-2xl font-bold">{gatewayStats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Đang hoạt động
                    </p>
                    <p className="text-2xl font-bold">{gatewayStats.active}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <XCircle className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Tạm dừng
                    </p>
                    <p className="text-2xl font-bold">
                      {gatewayStats.inactive}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Gateways Table */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Gateways</CardTitle>
            </CardHeader>
            <CardContent>
              <AdminDataTable
                config={gatewayTableConfig}
                dataSource={gateways || []}
                loading={gatewaysLoading}
                onRefresh={refreshGateways}
                onSearch={(search) => setGatewayParams({ search })}
                searchValue={gatewayParams.search || ""}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          {/* Transaction Stats */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <BarChart3 className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Tổng giao dịch
                    </p>
                    <p className="text-2xl font-bold">
                      {transactionStats.total}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Thành công
                    </p>
                    <p className="text-2xl font-bold">
                      {transactionStats.success}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Chờ xử lý
                    </p>
                    <p className="text-2xl font-bold">
                      {transactionStats.pending}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <XCircle className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Thất bại
                    </p>
                    <p className="text-2xl font-bold">
                      {transactionStats.failed}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      Tổng thu
                    </p>
                    <p className="text-2xl font-bold">
                      {transactionStats.totalAmount.toLocaleString()} VND
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Transactions Table */}
          <Card>
            <CardHeader>
              <CardTitle>Lịch sử Giao dịch</CardTitle>
            </CardHeader>
            <CardContent>
              <AdminDataTable
                config={transactionTableConfig}
                dataSource={transactions || []}
                loading={transactionsLoading}
                onRefresh={refreshTransactions}
                onSearch={(search) => setTransactionParams({ search })}
                searchValue={transactionParams.search || ""}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Báo cáo Thanh toán</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Báo cáo chi tiết</h3>
                <p className="text-muted-foreground">
                  Tính năng báo cáo chi tiết sẽ được triển khai trong phiên bản
                  tiếp theo
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Gateway Dialog */}
      <Dialog open={isCreateGatewayOpen} onOpenChange={setIsCreateGatewayOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Thêm Payment Gateway Mới</DialogTitle>
          </DialogHeader>
          <CreateGatewayForm
            onSuccess={() => {
              setIsCreateGatewayOpen(false);
              refreshGateways();
            }}
            onCancel={() => setIsCreateGatewayOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Create Gateway Form Component
function CreateGatewayForm({
  onSuccess,
  onCancel,
}: {
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    name: "",
    provider: "",
    description: "",
    apiKey: "",
    secretKey: "",
    webhookUrl: "",
    supportedMethods: [] as string[],
    isActive: true,
    isDefault: false,
    testMode: true,
  });

  const { create: createGateway } = useAdminCrud("/api/admin/payment-gateways");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const result = await createGateway(formData);
    if (result) {
      onSuccess();
    }
  };

  const handleMethodToggle = (method: string) => {
    setFormData((prev) => ({
      ...prev,
      supportedMethods: prev.supportedMethods.includes(method)
        ? prev.supportedMethods.filter((m) => m !== method)
        : [...prev.supportedMethods, method],
    }));
  };

  const availableMethods = [
    "CREDIT_CARD",
    "DEBIT_CARD",
    "BANK_TRANSFER",
    "E_WALLET",
    "QR_CODE",
    "CASH_ON_DELIVERY",
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-2">
          <Label htmlFor="name">Tên Gateway *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />
        </div>

        <div>
          <Label htmlFor="provider">Nhà cung cấp *</Label>
          <Select
            value={formData.provider}
            onValueChange={(value) =>
              setFormData({ ...formData, provider: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Chọn nhà cung cấp..." />
            </SelectTrigger>
            <SelectContent>
              {paymentProviders.map((provider) => (
                <SelectItem key={provider.value} value={provider.value}>
                  {provider.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="apiKey">API Key *</Label>
          <Input
            id="apiKey"
            type="password"
            value={formData.apiKey}
            onChange={(e) =>
              setFormData({ ...formData, apiKey: e.target.value })
            }
            required
          />
        </div>

        <div>
          <Label htmlFor="secretKey">Secret Key *</Label>
          <Input
            id="secretKey"
            type="password"
            value={formData.secretKey}
            onChange={(e) =>
              setFormData({ ...formData, secretKey: e.target.value })
            }
            required
          />
        </div>

        <div>
          <Label htmlFor="webhookUrl">Webhook URL</Label>
          <Input
            id="webhookUrl"
            type="url"
            value={formData.webhookUrl}
            onChange={(e) =>
              setFormData({ ...formData, webhookUrl: e.target.value })
            }
          />
        </div>

        <div className="col-span-2">
          <Label htmlFor="description">Mô tả</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            rows={3}
          />
        </div>

        <div className="col-span-2">
          <Label>Phương thức thanh toán hỗ trợ</Label>
          <div className="grid grid-cols-3 gap-2 mt-2">
            {availableMethods.map((method) => (
              <div key={method} className="flex items-center space-x-2">
                <Checkbox
                  id={method}
                  checked={formData.supportedMethods.includes(method)}
                  onCheckedChange={() => handleMethodToggle(method)}
                />
                <Label htmlFor={method} className="text-sm">
                  {method.replace(/_/g, " ")}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div className="col-span-2 flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="testMode"
              checked={formData.testMode}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, testMode: checked })
              }
            />
            <Label htmlFor="testMode">Chế độ test</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isDefault"
              checked={formData.isDefault}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isDefault: checked })
              }
            />
            <Label htmlFor="isDefault">Gateway mặc định</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isActive: checked })
              }
            />
            <Label htmlFor="isActive">Kích hoạt</Label>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Hủy
        </Button>
        <Button type="submit">Tạo Gateway</Button>
      </div>
    </form>
  );
}
