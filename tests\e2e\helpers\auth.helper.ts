import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext } from "@playwright/test";
import { AdminLoginPage } from "../pages/admin-login.page";

export interface AdminUser {
  email: string;
  password: string;
  role: "ADMIN" | "MODERATOR";
  name: string;
}

export const TEST_USERS: Record<string, AdminUser> = {
  admin: {
    email: "<EMAIL>",
    password: "admin123",
    role: "ADMIN",
    name: "Admin User",
  },
  moderator: {
    email: "<EMAIL>",
    password: "moderator123",
    role: "MODERAT<PERSON>",
    name: "Moderator User",
  },
  testAdmin: {
    email: "<EMAIL>",
    password: "testpassword123",
    role: "ADMI<PERSON>",
    name: "Test Admin",
  },
  testModerator: {
    email: "<EMAIL>",
    password: "testpassword123",
    role: "MODERATOR",
    name: "Test Moderator",
  },
};

/**
 * <PERSON><PERSON> as admin user and return authenticated page
 */
export async function loginAsAdmin(page: Page): Promise<void> {
  const loginPage = new AdminLoginPage(page);
  await loginPage.goto();
  await loginPage.waitForPageLoad();
  await loginPage.login(TEST_USERS.admin.email, TEST_USERS.admin.password);
  await loginPage.expectLoginSuccess();
}

/**
 * Login as moderator user and return authenticated page
 */
export async function loginAsModerator(page: Page): Promise<void> {
  const loginPage = new AdminLoginPage(page);
  await loginPage.goto();
  await loginPage.waitForPageLoad();
  await loginPage.login(
    TEST_USERS.moderator.email,
    TEST_USERS.moderator.password
  );
  await loginPage.expectLoginSuccess();
}

/**
 * Login with custom credentials
 */
export async function loginWithCredentials(
  page: Page,
  email: string,
  password: string
): Promise<void> {
  const loginPage = new AdminLoginPage(page);
  await loginPage.goto();
  await loginPage.waitForPageLoad();
  await loginPage.login(email, password);
  await loginPage.expectLoginSuccess();
}

/**
 * Login with predefined test user
 */
export async function loginAsUser(
  page: Page,
  userKey: keyof typeof TEST_USERS
): Promise<void> {
  const user = TEST_USERS[userKey];
  await loginWithCredentials(page, user.email, user.password);
}

/**
 * Create authenticated browser context for admin
 */
export async function createAdminContext(
  browser: Browser
): Promise<BrowserContext> {
  const context = await browser.newContext();
  const page = await context.newPage();
  await loginAsAdmin(page);
  await page.close();
  return context;
}

/**
 * Create authenticated browser context for moderator
 */
export async function createModeratorContext(
  browser: Browser
): Promise<BrowserContext> {
  const context = await browser.newContext();
  const page = await context.newPage();
  await loginAsModerator(page);
  await page.close();
  return context;
}

/**
 * Logout from admin panel
 */
export async function logout(page: Page): Promise<void> {
  // Navigate to logout or clear session
  await page.goto("/admin/auth/signout");
  await page.waitForURL("/admin/auth/signin");
}

/**
 * Check if user is authenticated and on admin dashboard
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    await page.goto("/admin");
    await page.waitForURL("/admin", { timeout: 5000 });
    return true;
  } catch {
    return false;
  }
}

/**
 * Ensure user is logged out
 */
export async function ensureLoggedOut(page: Page): Promise<void> {
  const authenticated = await isAuthenticated(page);
  if (authenticated) {
    await logout(page);
  }
}

/**
 * Get current user info from page context
 */
export async function getCurrentUser(page: Page): Promise<any> {
  return await page.evaluate(() => {
    // This would need to be adapted based on how user info is stored in the app
    return (window as any).__NEXT_DATA__?.props?.pageProps?.session?.user;
  });
}

/**
 * Wait for authentication to complete
 */
export async function waitForAuth(
  page: Page,
  timeout: number = 10000
): Promise<void> {
  await page.waitForFunction(
    () => {
      // Check if we're on admin dashboard or have session
      return (
        window.location.pathname.startsWith("/admin") &&
        !window.location.pathname.includes("/auth/signin")
      );
    },
    { timeout }
  );
}
