import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { z } from 'zod';

const productSchema = z.object({
	name: z.string().min(1, 'Tên sản phẩm là bắt buộc'),
	description: z.string().min(1, '<PERSON><PERSON> tả sản phẩm là bắt buộc'),
	price: z.number().positive('Giá phải lớn hơn 0'),
	salePrice: z.number().positive().optional(),
	images: z.array(z.string()).min(1, 'Phải có ít nhất 1 hình ảnh'),
	categoryId: z.string().min(1, '<PERSON>h mục là bắt buộc'),
	stock: z.number().int().min(0, 'S<PERSON> lượng không được âm'),
	sku: z.string().min(1, 'SKU là bắt buộc'),
	featured: z.boolean().optional(),
	tags: z.array(z.string()).optional(),
});

// GET /api/products - Lấy danh sách sản phẩm
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '12');
		const category = searchParams.get('category');
		const search = searchParams.get('search');
		const featured = searchParams.get('featured');
		const sortBy = searchParams.get('sortBy') || 'createdAt';
		const sortOrder = searchParams.get('sortOrder') || 'desc';

		const skip = (page - 1) * limit;

		// Build where clause
		const where: any = {
			status: 'ACTIVE',
		};

		if (category) {
			where.categoryId = category;
		}

		if (search) {
			where.OR = [
				{ name: { contains: search, mode: 'insensitive' } },
				{ description: { contains: search, mode: 'insensitive' } },
				{ tags: { has: search } },
			];
		}

		if (featured === 'true') {
			where.featured = true;
		}

		// Get products with pagination
		const [products, total] = await Promise.all([
			prisma.product.findMany({
				where,
				include: {
					category: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					reviews: {
						select: {
							rating: true,
						},
					},
				},
				orderBy: {
					[sortBy]: sortOrder,
				},
				skip,
				take: limit,
			}),
			prisma.product.count({ where }),
		]);

		// Calculate average rating for each product
		const productsWithRating = products.map((product) => {
			const avgRating = product.reviews.length > 0
				? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
				: 0;

			return {
				...product,
				avgRating: Math.round(avgRating * 10) / 10,
				reviewCount: product.reviews.length,
				reviews: undefined, // Remove reviews from response
			};
		});

		return NextResponse.json({
			products: productsWithRating,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get products error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách sản phẩm' },
			{ status: 500 }
		);
	}
}

// POST /api/products - Tạo sản phẩm mới (Admin only)
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const body = await request.json();
		const data = productSchema.parse(body);

		// Generate slug from name
		const slug = data.name
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();

		// Check if SKU already exists
		const existingSku = await prisma.product.findUnique({
			where: { sku: data.sku },
		});

		if (existingSku) {
			return NextResponse.json(
				{ error: 'SKU đã tồn tại' },
				{ status: 400 }
			);
		}

		// Check if slug already exists
		let finalSlug = slug;
		let counter = 1;
		while (await prisma.product.findUnique({ where: { slug: finalSlug } })) {
			finalSlug = `${slug}-${counter}`;
			counter++;
		}

		const product = await prisma.product.create({
			data: {
				...data,
				slug: finalSlug,
				tags: data.tags || [],
			},
			include: {
				category: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
			},
		});

		return NextResponse.json(
			{
				message: 'Tạo sản phẩm thành công',
				product,
			},
			{ status: 201 }
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Create product error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi tạo sản phẩm' },
			{ status: 500 }
		);
	}
}
