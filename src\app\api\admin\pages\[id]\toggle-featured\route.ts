import { NextRequest, NextResponse } from "next/server";
import { PageController } from "@/lib/admin/controllers/PageController";
import { verifyAdminToken } from "@/lib/admin-auth";

const pageController = new PageController();

// PATCH /api/admin/pages/[id]/toggle-featured - Toggle featured status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "<PERSON>hông có quyền truy cập" },
        { status: 401 }
      );
    }

    const result = await pageController.toggleFeatured(params.id);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Toggle featured error:", error);
    return NextResponse.json(
      { success: false, error: "C<PERSON> lỗi xảy ra khi cập nhật trạng thái nổi bật" },
      { status: 500 }
    );
  }
}
