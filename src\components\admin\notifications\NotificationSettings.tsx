"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Settings,
  Mail,
  Bell,
  Clock,
  Shield,
  TestTube,
  Save,
  RefreshCw,
  ArrowLeft,
} from "lucide-react";
import { toast } from "sonner";
import {
  NotificationPreferences,
  DEFAULT_PREFERENCES,
} from "@/lib/notification-preferences";

interface NotificationSettingsProps {
  adminId: string;
}

export function NotificationSettings({ adminId }: NotificationSettingsProps) {
  const router = useRouter();
  const [preferences, setPreferences] = useState<NotificationPreferences>(DEFAULT_PREFERENCES);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [emailStatus, setEmailStatus] = useState<{
    configured: boolean;
    connected: boolean;
    status: string;
  } | null>(null);

  useEffect(() => {
    loadPreferences();
    checkEmailStatus();
  }, [adminId]);

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/notifications/preferences?adminId=${adminId}`);
      if (response.ok) {
        const data = await response.json();
        setPreferences(data.preferences || DEFAULT_PREFERENCES);
      }
    } catch (error) {
      console.error("Error loading preferences:", error);
      toast.error("Không thể tải cài đặt thông báo");
    } finally {
      setLoading(false);
    }
  };

  const checkEmailStatus = async () => {
    try {
      const response = await fetch("/api/admin/notifications/email");
      if (response.ok) {
        const data = await response.json();
        setEmailStatus(data);
      }
    } catch (error) {
      console.error("Error checking email status:", error);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      const response = await fetch("/api/admin/notifications/preferences", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          adminId,
          preferences,
        }),
      });

      if (response.ok) {
        toast.success("Đã lưu cài đặt thành công");
      } else {
        throw new Error("Failed to save preferences");
      }
    } catch (error) {
      console.error("Error saving preferences:", error);
      toast.error("Không thể lưu cài đặt");
    } finally {
      setSaving(false);
    }
  };

  const testEmailNotification = async () => {
    try {
      setTesting(true);
      const response = await fetch("/api/admin/notifications/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test",
          recipient: "<EMAIL>", // This should be the current admin's email
        }),
      });

      if (response.ok) {
        toast.success("Email test đã được gửi");
      } else {
        const error = await response.json();
        toast.error(error.error || "Không thể gửi email test");
      }
    } catch (error) {
      console.error("Error testing email:", error);
      toast.error("Không thể gửi email test");
    } finally {
      setTesting(false);
    }
  };

  const updateEmailPreference = (key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      emailNotifications: {
        ...prev.emailNotifications,
        [key]: value,
      },
    }));
  };

  const updateBrowserPreference = (key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      browserNotifications: {
        ...prev.browserNotifications,
        [key]: value,
      },
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Settings className="h-8 w-8" />
              Cài đặt Thông báo
            </h1>
            <p className="text-muted-foreground">
              Quản lý preferences và cài đặt thông báo cá nhân
            </p>
          </div>
        </div>
        <Button onClick={savePreferences} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? "Đang lưu..." : "Lưu cài đặt"}
        </Button>
      </div>

      {/* Email Status */}
      {emailStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Trạng thái Email Service
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm">
                  Cấu hình: {" "}
                  <Badge variant={emailStatus.configured ? "default" : "destructive"}>
                    {emailStatus.configured ? "Đã cấu hình" : "Chưa cấu hình"}
                  </Badge>
                </p>
                <p className="text-sm">
                  Kết nối: {" "}
                  <Badge variant={emailStatus.connected ? "default" : "destructive"}>
                    {emailStatus.connected ? "Hoạt động" : "Lỗi"}
                  </Badge>
                </p>
                <p className="text-xs text-muted-foreground">{emailStatus.status}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={testEmailNotification}
                disabled={!emailStatus.configured || testing}
              >
                <TestTube className="h-4 w-4 mr-2" />
                {testing ? "Đang test..." : "Test Email"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Cài đặt thông báo qua email
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable Email Notifications */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Bật thông báo email</Label>
              <p className="text-sm text-muted-foreground">
                Nhận thông báo qua email
              </p>
            </div>
            <Switch
              checked={preferences.emailNotifications.enabled}
              onCheckedChange={(checked) => updateEmailPreference("enabled", checked)}
            />
          </div>

          <Separator />

          {/* Email Types */}
          <div className="space-y-4">
            <Label>Loại thông báo</Label>
            {Object.entries(preferences.emailNotifications.types).map(([type, enabled]) => (
              <div key={type} className="flex items-center justify-between">
                <Label className="text-sm">{type}</Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    updateEmailPreference("types", {
                      ...preferences.emailNotifications.types,
                      [type]: checked,
                    })
                  }
                  disabled={!preferences.emailNotifications.enabled}
                />
              </div>
            ))}
          </div>

          <Separator />

          {/* Email Priorities */}
          <div className="space-y-4">
            <Label>Độ ưu tiên</Label>
            {Object.entries(preferences.emailNotifications.priorities).map(([priority, enabled]) => (
              <div key={priority} className="flex items-center justify-between">
                <Label className="text-sm">{priority}</Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    updateEmailPreference("priorities", {
                      ...preferences.emailNotifications.priorities,
                      [priority]: checked,
                    })
                  }
                  disabled={!preferences.emailNotifications.enabled}
                />
              </div>
            ))}
          </div>

          <Separator />

          {/* Email Frequency */}
          <div className="space-y-2">
            <Label>Tần suất gửi email</Label>
            <Select
              value={preferences.emailNotifications.frequency}
              onValueChange={(value) => updateEmailPreference("frequency", value)}
              disabled={!preferences.emailNotifications.enabled}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Ngay lập tức</SelectItem>
                <SelectItem value="hourly">Mỗi giờ</SelectItem>
                <SelectItem value="daily">Hàng ngày</SelectItem>
                <SelectItem value="weekly">Hàng tuần</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Quiet Hours */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Giờ im lặng</Label>
                <p className="text-sm text-muted-foreground">
                  Không gửi email trong khoảng thời gian này
                </p>
              </div>
              <Switch
                checked={preferences.emailNotifications.quietHours.enabled}
                onCheckedChange={(checked) => 
                  updateEmailPreference("quietHours", {
                    ...preferences.emailNotifications.quietHours,
                    enabled: checked,
                  })
                }
                disabled={!preferences.emailNotifications.enabled}
              />
            </div>

            {preferences.emailNotifications.quietHours.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Từ</Label>
                  <Input
                    type="time"
                    value={preferences.emailNotifications.quietHours.start}
                    onChange={(e) => 
                      updateEmailPreference("quietHours", {
                        ...preferences.emailNotifications.quietHours,
                        start: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label>Đến</Label>
                  <Input
                    type="time"
                    value={preferences.emailNotifications.quietHours.end}
                    onChange={(e) => 
                      updateEmailPreference("quietHours", {
                        ...preferences.emailNotifications.quietHours,
                        end: e.target.value,
                      })
                    }
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Browser Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Browser Notifications
          </CardTitle>
          <CardDescription>
            Cài đặt thông báo trên trình duyệt
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable Browser Notifications */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Bật thông báo trình duyệt</Label>
              <p className="text-sm text-muted-foreground">
                Hiển thị thông báo real-time
              </p>
            </div>
            <Switch
              checked={preferences.browserNotifications.enabled}
              onCheckedChange={(checked) => updateBrowserPreference("enabled", checked)}
            />
          </div>

          <Separator />

          {/* Browser Types */}
          <div className="space-y-4">
            <Label>Loại thông báo</Label>
            {Object.entries(preferences.browserNotifications.types).map(([type, enabled]) => (
              <div key={type} className="flex items-center justify-between">
                <Label className="text-sm">{type}</Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    updateBrowserPreference("types", {
                      ...preferences.browserNotifications.types,
                      [type]: checked,
                    })
                  }
                  disabled={!preferences.browserNotifications.enabled}
                />
              </div>
            ))}
          </div>

          <Separator />

          {/* Browser Priorities */}
          <div className="space-y-4">
            <Label>Độ ưu tiên</Label>
            {Object.entries(preferences.browserNotifications.priorities).map(([priority, enabled]) => (
              <div key={priority} className="flex items-center justify-between">
                <Label className="text-sm">{priority}</Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    updateBrowserPreference("priorities", {
                      ...preferences.browserNotifications.priorities,
                      [priority]: checked,
                    })
                  }
                  disabled={!preferences.browserNotifications.enabled}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Auto Mark as Read */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Tự động đánh dấu đã đọc
          </CardTitle>
          <CardDescription>
            Tự động đánh dấu thông báo cũ là đã đọc
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Bật tự động đánh dấu</Label>
              <p className="text-sm text-muted-foreground">
                Đánh dấu thông báo cũ là đã đọc
              </p>
            </div>
            <Switch
              checked={preferences.autoMarkAsRead.enabled}
              onCheckedChange={(checked) => 
                setPreferences(prev => ({
                  ...prev,
                  autoMarkAsRead: {
                    ...prev.autoMarkAsRead,
                    enabled: checked,
                  },
                }))
              }
            />
          </div>

          {preferences.autoMarkAsRead.enabled && (
            <div className="space-y-2">
              <Label>Sau số ngày</Label>
              <Select
                value={preferences.autoMarkAsRead.afterDays.toString()}
                onValueChange={(value) => 
                  setPreferences(prev => ({
                    ...prev,
                    autoMarkAsRead: {
                      ...prev.autoMarkAsRead,
                      afterDays: parseInt(value),
                    },
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 ngày</SelectItem>
                  <SelectItem value="14">14 ngày</SelectItem>
                  <SelectItem value="30">30 ngày</SelectItem>
                  <SelectItem value="60">60 ngày</SelectItem>
                  <SelectItem value="90">90 ngày</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
