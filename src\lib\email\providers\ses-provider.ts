import { SE<PERSON><PERSON>, SendEmailCommand, GetSendQuotaCommand } from '@aws-sdk/client-ses';
import { IEmailProvider, BulkEmailResult } from './interface';
import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generateContactFormEmail,
  generatePasswordResetEmail,
  generateAdminNotificationEmail,
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from '../templates';

interface SendEmailOptions {
  to: string;
  toName?: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
  fromName?: string;
}

export class SESProvider implements IEmailProvider {
  private client: SESClient | null = null;
  private config: {
    region: string;
    from: {
      name: string;
      address: string;
    };
  } | null = null;
  private initialized = false;

  async initialize(): Promise<boolean> {
    try {
      const region = process.env.AWS_REGION || 'us-east-1';
      const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
      const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
      
      // Check if we have AWS credentials
      if (!accessKeyId || !secretAccessKey) {
        console.warn("AWS credentials not configured for SES");
        return false;
      }

      this.config = {
        region,
        from: {
          name: process.env.SES_FROM_NAME || process.env.EMAIL_FROM_NAME || "NS Shop",
          address: process.env.SES_FROM_EMAIL || process.env.EMAIL_FROM || "<EMAIL>",
        },
      };

      // Initialize SES client
      this.client = new SESClient({
        region: this.config.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
      
      this.initialized = true;
      console.log("Amazon SES email provider initialized successfully");
      return true;
    } catch (error) {
      console.error("Failed to initialize Amazon SES email provider:", error);
      return false;
    }
  }

  private async sendEmail(options: SendEmailOptions): Promise<boolean> {
    if (!this.initialized || !this.client || !this.config) {
      console.warn("Amazon SES email provider not initialized");
      return false;
    }

    try {
      const fromAddress = options.from || this.config.from.address;
      const fromName = options.fromName || this.config.from.name;
      const fromFormatted = `${fromName} <${fromAddress}>`;

      const command = new SendEmailCommand({
        Source: fromFormatted,
        Destination: {
          ToAddresses: [
            options.toName 
              ? `${options.toName} <${options.to}>`
              : options.to
          ],
        },
        Message: {
          Subject: {
            Data: options.subject,
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: options.html,
              Charset: 'UTF-8',
            },
            Text: {
              Data: options.text,
              Charset: 'UTF-8',
            },
          },
        },
      });

      const result = await this.client.send(command);
      console.log("Amazon SES email sent successfully:", result.MessageId);
      return true;
    } catch (error) {
      console.error("Failed to send email via Amazon SES:", error);
      return false;
    }
  }

  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    const template = generateWelcomeEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendOrderConfirmationEmail(data: OrderConfirmationEmailData): Promise<boolean> {
    const template = generateOrderConfirmationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendContactFormEmail(data: ContactFormEmailData): Promise<boolean> {
    const template = generateContactFormEmail(data);
    
    // Send to admin/support email
    const adminEmail = process.env.ADMIN_EMAIL || process.env.EMAIL_FROM || "<EMAIL>";
    
    return this.sendEmail({
      to: adminEmail,
      toName: "NS Shop Admin",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordResetEmail(data: PasswordResetEmailData): Promise<boolean> {
    const template = generatePasswordResetEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendAdminNotificationEmail(data: AdminNotificationEmailData): Promise<boolean> {
    const template = generateAdminNotificationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendBulkAdminNotificationEmails(
    notifications: AdminNotificationEmailData[]
  ): Promise<BulkEmailResult> {
    const results: BulkEmailResult = {
      sent: 0,
      failed: 0,
      errors: [],
    };

    for (const notification of notifications) {
      try {
        const success = await this.sendAdminNotificationEmail(notification);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
          results.errors.push(
            `Failed to send to ${notification.recipientEmail}`
          );
        }
      } catch (error) {
        results.failed++;
        results.errors.push(
          `Error sending to ${notification.recipientEmail}: ${error}`
        );
      }

      // Add small delay to avoid overwhelming SES
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return results;
  }

  async testConnection(): Promise<boolean> {
    if (!this.initialized || !this.client) {
      return false;
    }

    try {
      // Test connection by getting send quota
      const command = new GetSendQuotaCommand({});
      await this.client.send(command);
      return true;
    } catch (error) {
      console.error("Amazon SES connection test failed:", error);
      return false;
    }
  }

  async sendTestEmail(recipientEmail: string, recipientName: string = "Test User"): Promise<boolean> {
    const testEmailData: AdminNotificationEmailData = {
      recipientName,
      recipientEmail,
      notification: {
        id: "test",
        title: "Test Email từ NS Shop (Amazon SES)",
        message: "Đây là email test để kiểm tra cấu hình Amazon SES. Nếu bạn nhận được email này, Amazon SES đã hoạt động bình thường.",
        type: "INFO",
        priority: "NORMAL",
        createdAt: new Date().toISOString(),
      },
    };

    return this.sendAdminNotificationEmail(testEmailData);
  }

  getProviderName(): string {
    return "Amazon SES";
  }

  async getStatus(): Promise<{
    connected: boolean;
    configured: boolean;
    message: string;
    provider: string;
  }> {
    const configured = !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY);
    let connected = false;

    if (this.initialized && configured) {
      connected = await this.testConnection();
    }

    return {
      connected,
      configured,
      message: connected 
        ? "Amazon SES is connected and ready" 
        : configured 
          ? "Amazon SES is configured but not connected"
          : "Amazon SES is not configured",
      provider: "Amazon SES",
    };
  }
}
