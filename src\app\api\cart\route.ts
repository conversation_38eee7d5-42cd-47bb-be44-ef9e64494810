import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";

const addToCartSchema = z.object({
  productId: z.string().min(1, "Product ID là bắt buộc"),
  quantity: z.number().int().min(1, "Số lượng phải lớn hơn 0"),
});

const updateCartItemSchema = z.object({
  quantity: z.number().int().min(0, "Số lượng không được âm"),
});

// GET /api/cart - Lấy giỏ hàng của user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "<PERSON>ui lòng đăng nhập" },
        { status: 401 }
      );
    }

    console.log("Session user ID:", session.user.id);

    // Kiểm tra user có tồn tại trong database không
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      console.error("User not found in database:", session.user.id);
      return NextResponse.json(
        { error: "Người dùng không tồn tại" },
        { status: 404 }
      );
    }

    const cart = await prisma.cart.findUnique({
      where: { userId: session.user.id },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
                salePrice: true,
                images: true,
                slug: true,
                stock: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!cart) {
      // Tạo giỏ hàng mới nếu chưa có
      try {
        const newCart = await prisma.cart.create({
          data: {
            userId: session.user.id,
          },
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    price: true,
                    salePrice: true,
                    images: true,
                    slug: true,
                    stock: true,
                    status: true,
                  },
                },
              },
            },
          },
        });

        return NextResponse.json(newCart);
      } catch (createError) {
        console.error("Error creating cart:", createError);
        return NextResponse.json(
          { error: "Không thể tạo giỏ hàng" },
          { status: 500 }
        );
      }
    }

    // Calculate totals
    const subtotal = cart.items.reduce((sum, item) => {
      const price = item.product.salePrice || item.product.price;
      return sum + price * item.quantity;
    }, 0);

    const cartWithTotals = {
      ...cart,
      subtotal,
      itemCount: cart.items.reduce((sum, item) => sum + item.quantity, 0),
    };

    return NextResponse.json(cartWithTotals);
  } catch (error) {
    console.error("Get cart error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy giỏ hàng" },
      { status: 500 }
    );
  }
}

// POST /api/cart - Thêm sản phẩm vào giỏ hàng
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Kiểm tra user có tồn tại trong database không
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      console.error("User not found in database:", session.user.id);
      return NextResponse.json(
        { error: "Người dùng không tồn tại" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { productId, quantity } = addToCartSchema.parse(body);

    // Kiểm tra sản phẩm có tồn tại và còn hàng
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Sản phẩm không tồn tại" },
        { status: 404 }
      );
    }

    if (product.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Sản phẩm không khả dụng" },
        { status: 400 }
      );
    }

    if (product.stock < quantity) {
      return NextResponse.json(
        { error: "Không đủ hàng trong kho" },
        { status: 400 }
      );
    }

    // Tìm hoặc tạo giỏ hàng
    let cart = await prisma.cart.findUnique({
      where: { userId: session.user.id },
    });

    if (!cart) {
      try {
        cart = await prisma.cart.create({
          data: { userId: session.user.id },
        });
      } catch (createError) {
        console.error("Error creating cart in POST:", createError);
        return NextResponse.json(
          { error: "Không thể tạo giỏ hàng" },
          { status: 500 }
        );
      }
    }

    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    const existingItem = await prisma.cartItem.findUnique({
      where: {
        cartId_productId: {
          cartId: cart.id,
          productId,
        },
      },
    });

    if (existingItem) {
      // Cập nhật số lượng
      const newQuantity = existingItem.quantity + quantity;

      if (product.stock < newQuantity) {
        return NextResponse.json(
          { error: "Không đủ hàng trong kho" },
          { status: 400 }
        );
      }

      await prisma.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: newQuantity },
      });
    } else {
      // Thêm sản phẩm mới
      await prisma.cartItem.create({
        data: {
          cartId: cart.id,
          productId,
          quantity,
          price: product.salePrice || product.price,
        },
      });
    }

    // Lấy giỏ hàng đã cập nhật
    const updatedCart = await prisma.cart.findUnique({
      where: { id: cart.id },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
                salePrice: true,
                images: true,
                slug: true,
                stock: true,
                status: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: "Thêm vào giỏ hàng thành công",
      cart: updatedCart,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Add to cart error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thêm vào giỏ hàng" },
      { status: 500 }
    );
  }
}
