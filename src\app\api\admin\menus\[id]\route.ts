import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAdminToken } from "@/lib/admin-auth";
import { z } from "zod";

const updateMenuSchema = z.object({
  name: z.string().min(1, "Tên menu không được để trống").optional(),
  location: z.string().min(1, "Vị trí menu không được để trống").optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/admin/menus/[id] - Get menu by ID
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { id } = params;

    const menu = await prisma.menu.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            children: {
              orderBy: { order: "asc" },
            },
          },
          where: { parentId: null }, // Only get top-level items
          orderBy: { order: "asc" },
        },
      },
    });

    if (!menu) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy menu" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: menu,
    });
  } catch (error) {
    console.error("Error fetching menu:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải menu" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menus/[id] - Update menu
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const validatedData = updateMenuSchema.parse(body);

    // Check if menu exists
    const existingMenu = await prisma.menu.findUnique({
      where: { id },
    });

    if (!existingMenu) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy menu" },
        { status: 404 }
      );
    }

    // Check for duplicate name and location (if updating these fields)
    if (validatedData.name || validatedData.location) {
      const duplicateMenu = await prisma.menu.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              name: validatedData.name || existingMenu.name,
              location: validatedData.location || existingMenu.location,
            },
          ],
        },
      });

      if (duplicateMenu) {
        return NextResponse.json(
          { success: false, error: "Menu với tên và vị trí này đã tồn tại" },
          { status: 400 }
        );
      }
    }

    const updatedMenu = await prisma.menu.update({
      where: { id },
      data: validatedData,
      include: {
        items: {
          include: {
            children: {
              orderBy: { order: "asc" },
            },
          },
          where: { parentId: null },
          orderBy: { order: "asc" },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedMenu,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error updating menu:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật menu" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menus/[id] - Delete menu
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Check if menu exists
    const existingMenu = await prisma.menu.findUnique({
      where: { id },
      include: {
        items: true,
      },
    });

    if (!existingMenu) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy menu" },
        { status: 404 }
      );
    }

    // Delete menu (menu items will be deleted automatically due to cascade)
    await prisma.menu.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "Xóa menu thành công",
    });
  } catch (error) {
    console.error("Error deleting menu:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu" },
      { status: 500 }
    );
  }
}
