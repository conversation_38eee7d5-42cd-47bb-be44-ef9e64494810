import { IEmailProvider, EmailProviderType } from './interface';
import { SendGridProvider } from './sendgrid-provider';
import { SESProvider } from './ses-provider';

export class EmailProviderFactory {
  private static instance: EmailProviderFactory;
  private providers: Map<EmailProviderType, IEmailProvider> = new Map();

  private constructor() {}

  static getInstance(): EmailProviderFactory {
    if (!EmailProviderFactory.instance) {
      EmailProviderFactory.instance = new EmailProviderFactory();
    }
    return EmailProviderFactory.instance;
  }

  /**
   * Create email provider based on type
   */
  createProvider(type: EmailProviderType): IEmailProvider {
    // Return cached provider if exists
    if (this.providers.has(type)) {
      return this.providers.get(type)!;
    }

    let provider: IEmailProvider;

    switch (type) {
      case 'sendgrid':
        provider = new SendGridProvider();
        break;
      case 'ses':
        provider = new SESProvider();
        break;
      case 'auto':
        // Auto mode: try to determine best available provider
        provider = this.createAutoProvider();
        break;
      default:
        throw new Error(`Unsupported email provider type: ${type}`);
    }

    // Cache the provider
    this.providers.set(type, provider);
    return provider;
  }

  /**
   * Create provider automatically based on available configuration
   */
  private createAutoProvider(): IEmailProvider {
    // Check SendGrid configuration first
    if (process.env.SENDGRID_API_KEY) {
      console.log("Auto-selecting SendGrid provider (API key found)");
      return new SendGridProvider();
    }

    // Check Amazon SES configuration
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      console.log("Auto-selecting Amazon SES provider (AWS credentials found)");
      return new SESProvider();
    }

    // Default to SendGrid if no configuration found
    console.warn("No email provider configuration found, defaulting to SendGrid");
    return new SendGridProvider();
  }

  /**
   * Get provider type from environment variable
   */
  static getProviderTypeFromEnv(): EmailProviderType {
    const envProvider = process.env.EMAIL_PROVIDER?.toLowerCase();
    
    switch (envProvider) {
      case 'sendgrid':
        return 'sendgrid';
      case 'ses':
      case 'amazon-ses':
      case 'aws-ses':
        return 'ses';
      case 'auto':
        return 'auto';
      default:
        return 'auto'; // Default to auto selection
    }
  }

  /**
   * Create provider based on environment configuration
   */
  static createFromEnv(): IEmailProvider {
    const factory = EmailProviderFactory.getInstance();
    const providerType = EmailProviderFactory.getProviderTypeFromEnv();
    return factory.createProvider(providerType);
  }

  /**
   * Get all available providers with their status
   */
  async getAllProvidersStatus(): Promise<Array<{
    type: EmailProviderType;
    name: string;
    configured: boolean;
    connected: boolean;
    message: string;
  }>> {
    const providers: EmailProviderType[] = ['sendgrid', 'ses'];
    const results = [];

    for (const type of providers) {
      try {
        const provider = this.createProvider(type);
        await provider.initialize();
        const status = await provider.getStatus();
        
        results.push({
          type,
          name: provider.getProviderName(),
          configured: status.configured,
          connected: status.connected,
          message: status.message,
        });
      } catch (error) {
        results.push({
          type,
          name: type === 'sendgrid' ? 'SendGrid' : 'Amazon SES',
          configured: false,
          connected: false,
          message: `Error: ${error}`,
        });
      }
    }

    return results;
  }

  /**
   * Test all available providers
   */
  async testAllProviders(testEmail: string): Promise<Array<{
    provider: string;
    success: boolean;
    error?: string;
  }>> {
    const providers: EmailProviderType[] = ['sendgrid', 'ses'];
    const results = [];

    for (const type of providers) {
      try {
        const provider = this.createProvider(type);
        const initialized = await provider.initialize();
        
        if (!initialized) {
          results.push({
            provider: provider.getProviderName(),
            success: false,
            error: 'Provider not initialized',
          });
          continue;
        }

        const success = await provider.sendTestEmail(testEmail);
        results.push({
          provider: provider.getProviderName(),
          success,
          error: success ? undefined : 'Failed to send test email',
        });
      } catch (error) {
        results.push({
          provider: type === 'sendgrid' ? 'SendGrid' : 'Amazon SES',
          success: false,
          error: `Error: ${error}`,
        });
      }
    }

    return results;
  }

  /**
   * Clear cached providers (useful for testing)
   */
  clearCache(): void {
    this.providers.clear();
  }
}

// Export singleton instance
export const emailProviderFactory = EmailProviderFactory.getInstance();
