import {
  onProductStockUpdate,
  onProductCreated,
  onOrderCreated,
  onOrderStatusChanged,
  onPaymentFailed,
  onPaymentSuccess,
  onUserRegistered,
  onUserSuspended,
  onSystemError,
  onAdminLogin,
  onAdminPermissionChanged,
  onInventoryAlert,
  initializeNotificationTriggers,
} from "@/lib/notification-triggers";

/**
 * Event Service - Central service for handling system events and triggering notifications
 */

export class EventService {
  private static instance: EventService;
  private initialized = false;

  static getInstance(): EventService {
    if (!EventService.instance) {
      EventService.instance = new EventService();
    }
    return EventService.instance;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      await initializeNotificationTriggers();
      this.initialized = true;
      console.log("Event Service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Event Service:", error);
    }
  }

  // Product Events
  async productCreated(product: any, createdBy: string) {
    if (!this.initialized) await this.initialize();

    try {
      await onProductCreated(product, createdBy);

      // Check for low stock on creation
      if (product.stock <= 10) {
        await onInventoryAlert(
          product.id,
          product.name,
          product.stock,
          product.reorderLevel || 10
        );
      }
    } catch (error) {
      console.error("Error handling product created event:", error);
    }
  }

  async productUpdated(
    productId: string,
    oldProduct: any,
    newProduct: any,
    updatedBy: string
  ) {
    if (!this.initialized) await this.initialize();

    try {
      // Stock change detection
      if (oldProduct.stock !== newProduct.stock) {
        await onProductStockUpdate(
          productId,
          oldProduct.stock,
          newProduct.stock,
          newProduct.name
        );
      }

      // Price change notification for significant changes
      if (oldProduct.price !== newProduct.price) {
        const priceChangePercent = Math.abs(
          ((newProduct.price - oldProduct.price) / oldProduct.price) * 100
        );

        if (priceChangePercent > 20) {
          // More than 20% change
          await this.emit(
            "product.price_change_significant",
            {
              productId,
              productName: newProduct.name,
              oldPrice: oldProduct.price,
              newPrice: newProduct.price,
              changePercent: priceChangePercent,
            },
            updatedBy
          );
        }
      }

      // Status change notification
      if (oldProduct.status !== newProduct.status) {
        await this.emit(
          "product.status_changed",
          {
            productId,
            productName: newProduct.name,
            oldStatus: oldProduct.status,
            newStatus: newProduct.status,
          },
          updatedBy
        );
      }
    } catch (error) {
      console.error("Error handling product updated event:", error);
    }
  }

  // Order Events
  async orderCreated(order: any) {
    if (!this.initialized) await this.initialize();

    try {
      await onOrderCreated(order);

      // Check for inventory impact
      if (order.items) {
        for (const item of order.items) {
          const product = await this.getProduct(item.productId);
          if (product && product.stock <= 10) {
            await onInventoryAlert(
              product.id,
              product.name,
              product.stock,
              10 // Default reorder level since it's not in the schema
            );
          }
        }
      }
    } catch (error) {
      console.error("Error handling order created event:", error);
    }
  }

  async orderStatusUpdated(
    orderId: string,
    orderNumber: string,
    oldStatus: string,
    newStatus: string,
    updatedBy?: string
  ) {
    if (!this.initialized) await this.initialize();

    try {
      await onOrderStatusChanged(
        orderId,
        orderNumber,
        oldStatus,
        newStatus,
        updatedBy
      );

      // Special handling for completed orders
      if (newStatus === "COMPLETED") {
        await this.emit(
          "order.completed",
          {
            orderId,
            orderNumber,
            completedAt: new Date().toISOString(),
          },
          updatedBy
        );
      }

      // Handle shipping status
      if (newStatus === "SHIPPED") {
        await this.emit(
          "order.shipped",
          {
            orderId,
            orderNumber,
            shippedAt: new Date().toISOString(),
          },
          updatedBy
        );
      }
    } catch (error) {
      console.error("Error handling order status updated event:", error);
    }
  }

  // Payment Events
  async paymentProcessed(
    orderId: string,
    orderNumber: string,
    amount: number,
    status: "success" | "failed",
    paymentMethod: string,
    reason?: string
  ) {
    if (!this.initialized) await this.initialize();

    try {
      if (status === "success") {
        await onPaymentSuccess(orderId, orderNumber, amount, paymentMethod);
      } else {
        await onPaymentFailed(
          orderId,
          orderNumber,
          reason || "Unknown error",
          amount
        );
      }
    } catch (error) {
      console.error("Error handling payment processed event:", error);
    }
  }

  // User Events
  async userRegistered(user: any) {
    if (!this.initialized) await this.initialize();

    try {
      await onUserRegistered(user);
    } catch (error) {
      console.error("Error handling user registered event:", error);
    }
  }

  async userSuspended(
    userId: string,
    userName: string,
    reason: string,
    suspendedBy: string
  ) {
    if (!this.initialized) await this.initialize();

    try {
      await onUserSuspended(userId, userName, reason, suspendedBy);
    } catch (error) {
      console.error("Error handling user suspended event:", error);
    }
  }

  // Admin Events
  async adminLoggedIn(adminId: string, adminName: string, ipAddress: string) {
    if (!this.initialized) await this.initialize();

    try {
      await onAdminLogin(adminId, adminName, ipAddress);
    } catch (error) {
      console.error("Error handling admin logged in event:", error);
    }
  }

  async adminPermissionsChanged(
    targetAdminId: string,
    targetAdminName: string,
    changedBy: string,
    oldPermissions: any,
    newPermissions: any
  ) {
    if (!this.initialized) await this.initialize();

    try {
      await onAdminPermissionChanged(
        targetAdminId,
        targetAdminName,
        changedBy,
        oldPermissions,
        newPermissions
      );
    } catch (error) {
      console.error("Error handling admin permissions changed event:", error);
    }
  }

  // System Events
  async systemError(
    error: Error,
    location: string,
    severity: "low" | "medium" | "high" | "critical" = "medium",
    additionalData?: any
  ) {
    if (!this.initialized) await this.initialize();

    try {
      await onSystemError(error, location, severity, additionalData);
    } catch (triggerError) {
      console.error("Error handling system error event:", triggerError);
    }
  }

  // Generic event emitter
  async emit(eventType: string, data: any, triggeredBy?: string) {
    if (!this.initialized) await this.initialize();

    try {
      const { notificationRulesEngine } = await import(
        "@/lib/notification-rules"
      );

      await notificationRulesEngine.processEvent({
        type: eventType,
        data,
        triggeredBy,
        timestamp: new Date(),
      });
    } catch (error) {
      console.error(`Error emitting event ${eventType}:`, error);
    }
  }

  // Helper methods
  private async getProduct(productId: string) {
    try {
      const { prisma } = await import("@/lib/prisma");
      return await prisma.product.findUnique({
        where: { id: productId },
        select: {
          id: true,
          name: true,
          stock: true,
        },
      });
    } catch (error) {
      console.error("Error fetching product:", error);
      return null;
    }
  }

  // Batch event processing
  async processBatch(
    events: Array<{
      type: string;
      data: any;
      triggeredBy?: string;
    }>
  ) {
    if (!this.initialized) await this.initialize();

    try {
      for (const event of events) {
        await this.emit(event.type, event.data, event.triggeredBy);
      }
    } catch (error) {
      console.error("Error processing batch events:", error);
    }
  }

  // Health check
  isHealthy(): boolean {
    return this.initialized;
  }

  // Get event statistics
  async getEventStats(timeRange: "1h" | "24h" | "7d" | "30d" = "24h") {
    try {
      const { prisma } = await import("@/lib/prisma");

      const timeRanges = {
        "1h": new Date(Date.now() - 60 * 60 * 1000),
        "24h": new Date(Date.now() - 24 * 60 * 60 * 1000),
        "7d": new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        "30d": new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      };

      const since = timeRanges[timeRange];

      const [notificationCount, auditLogCount] = await Promise.all([
        prisma.notification.count({
          where: {
            createdAt: { gte: since },
          },
        }),
        prisma.auditLog.count({
          where: {
            createdAt: { gte: since },
          },
        }),
      ]);

      return {
        timeRange,
        since: since.toISOString(),
        notificationCount,
        auditLogCount,
        totalEvents: notificationCount + auditLogCount,
      };
    } catch (error) {
      console.error("Error getting event stats:", error);
      return {
        timeRange,
        since: new Date().toISOString(),
        notificationCount: 0,
        auditLogCount: 0,
        totalEvents: 0,
      };
    }
  }
}

// Singleton instance
export const eventService = EventService.getInstance();

// Convenience functions for common events
export const Events = {
  // Product events
  productCreated: (product: any, createdBy: string) =>
    eventService.productCreated(product, createdBy),

  productUpdated: (
    productId: string,
    oldProduct: any,
    newProduct: any,
    updatedBy: string
  ) =>
    eventService.productUpdated(productId, oldProduct, newProduct, updatedBy),

  // Order events
  orderCreated: (order: any) => eventService.orderCreated(order),

  orderStatusUpdated: (
    orderId: string,
    orderNumber: string,
    oldStatus: string,
    newStatus: string,
    updatedBy?: string
  ) =>
    eventService.orderStatusUpdated(
      orderId,
      orderNumber,
      oldStatus,
      newStatus,
      updatedBy
    ),

  // Payment events
  paymentSuccess: (
    orderId: string,
    orderNumber: string,
    amount: number,
    paymentMethod: string
  ) =>
    eventService.paymentProcessed(
      orderId,
      orderNumber,
      amount,
      "success",
      paymentMethod
    ),

  paymentFailed: (
    orderId: string,
    orderNumber: string,
    amount: number,
    reason: string
  ) =>
    eventService.paymentProcessed(
      orderId,
      orderNumber,
      amount,
      "failed",
      "unknown",
      reason
    ),

  // User events
  userRegistered: (user: any) => eventService.userRegistered(user),

  userSuspended: (
    userId: string,
    userName: string,
    reason: string,
    suspendedBy: string
  ) => eventService.userSuspended(userId, userName, reason, suspendedBy),

  // Admin events
  adminLoggedIn: (adminId: string, adminName: string, ipAddress: string) =>
    eventService.adminLoggedIn(adminId, adminName, ipAddress),

  adminPermissionsChanged: (
    targetAdminId: string,
    targetAdminName: string,
    changedBy: string,
    oldPermissions: any,
    newPermissions: any
  ) =>
    eventService.adminPermissionsChanged(
      targetAdminId,
      targetAdminName,
      changedBy,
      oldPermissions,
      newPermissions
    ),

  // System events
  systemError: (
    error: Error,
    location: string,
    severity?: "low" | "medium" | "high" | "critical",
    additionalData?: any
  ) => eventService.systemError(error, location, severity, additionalData),

  // Generic event
  emit: (eventType: string, data: any, triggeredBy?: string) =>
    eventService.emit(eventType, data, triggeredBy),
};
