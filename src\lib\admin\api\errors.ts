import { NextResponse } from 'next/server';
import { AdminError } from '../types';
import { ResponseHandler } from './responses';

/**
 * Custom admin error class
 */
export class AdminApiError extends Error {
  public code: string;
  public statusCode: number;
  public field?: string;
  public details?: any;

  constructor(
    message: string,
    code: string = 'ADMIN_ERROR',
    statusCode: number = 400,
    field?: string,
    details?: any
  ) {
    super(message);
    this.name = 'AdminApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.field = field;
    this.details = details;
  }
}

/**
 * Validation error class
 */
export class ValidationError extends AdminApiError {
  constructor(message: string, field?: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, field, details);
    this.name = 'ValidationError';
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends AdminApiError {
  constructor(message: string = 'Không tìm thấy dữ liệu') {
    super(message, 'NOT_FOUND', 404);
    this.name = 'NotFoundError';
  }
}

/**
 * Unauthorized error class
 */
export class UnauthorizedError extends AdminApiError {
  constructor(message: string = 'Chưa đăng nhập') {
    super(message, 'UNAUTHORIZED', 401);
    this.name = 'UnauthorizedError';
  }
}

/**
 * Forbidden error class
 */
export class ForbiddenError extends AdminApiError {
  constructor(message: string = 'Không có quyền truy cập') {
    super(message, 'FORBIDDEN', 403);
    this.name = 'ForbiddenError';
  }
}

/**
 * Conflict error class
 */
export class ConflictError extends AdminApiError {
  constructor(message: string = 'Dữ liệu đã tồn tại') {
    super(message, 'CONFLICT', 409);
    this.name = 'ConflictError';
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends AdminApiError {
  constructor(message: string = 'Quá nhiều yêu cầu, vui lòng thử lại sau') {
    super(message, 'RATE_LIMIT', 429);
    this.name = 'RateLimitError';
  }
}

/**
 * Database error class
 */
export class DatabaseError extends AdminApiError {
  constructor(message: string = 'Lỗi cơ sở dữ liệu', details?: any) {
    super(message, 'DATABASE_ERROR', 500, undefined, details);
    this.name = 'DatabaseError';
  }
}

/**
 * Error handler function
 */
export function handleAdminError(error: any): NextResponse {
  console.error('Admin API Error:', error);

  // Handle custom admin errors
  if (error instanceof AdminApiError) {
    return ResponseHandler.error(error.message, error.statusCode, {
      code: error.code,
      field: error.field,
      details: error.details,
    });
  }

  // Handle Prisma errors
  if (error.code) {
    switch (error.code) {
      case 'P2002':
        return ResponseHandler.conflict('Dữ liệu đã tồn tại');
      case 'P2025':
        return ResponseHandler.notFound('Không tìm thấy dữ liệu');
      case 'P2003':
        return ResponseHandler.badRequest('Vi phạm ràng buộc khóa ngoại');
      case 'P2014':
        return ResponseHandler.badRequest('Vi phạm ràng buộc dữ liệu');
      default:
        return ResponseHandler.serverError('Lỗi cơ sở dữ liệu');
    }
  }

  // Handle Zod validation errors
  if (error.errors && Array.isArray(error.errors)) {
    const validationErrors: Record<string, string> = {};
    error.errors.forEach((err: any) => {
      const field = err.path.join('.');
      validationErrors[field] = err.message;
    });
    return ResponseHandler.validationError(validationErrors);
  }

  // Handle generic errors
  if (error instanceof Error) {
    return ResponseHandler.serverError(
      process.env.NODE_ENV === 'development' ? error.message : 'Lỗi máy chủ'
    );
  }

  // Fallback error
  return ResponseHandler.serverError('Lỗi không xác định');
}

/**
 * Async error wrapper for route handlers
 */
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleAdminError(error);
    }
  };
}

/**
 * Error logging utility
 */
export function logError(error: any, context?: string) {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    message: error.message,
    stack: error.stack,
    ...(error instanceof AdminApiError && {
      code: error.code,
      statusCode: error.statusCode,
      field: error.field,
      details: error.details,
    }),
  };

  console.error('Admin Error Log:', JSON.stringify(errorInfo, null, 2));

  // In production, you might want to send this to an external logging service
  if (process.env.NODE_ENV === 'production') {
    // Send to logging service (e.g., Sentry, LogRocket, etc.)
  }
}

/**
 * Error helper class
 */
export class ErrorHelper {
  static validation(message: string, field?: string, details?: any) {
    return new ValidationError(message, field, details);
  }

  static notFound(message?: string) {
    return new NotFoundError(message);
  }

  static unauthorized(message?: string) {
    return new UnauthorizedError(message);
  }

  static forbidden(message?: string) {
    return new ForbiddenError(message);
  }

  static conflict(message?: string) {
    return new ConflictError(message);
  }

  static rateLimit(message?: string) {
    return new RateLimitError(message);
  }

  static database(message?: string, details?: any) {
    return new DatabaseError(message, details);
  }

  static custom(
    message: string,
    code: string,
    statusCode: number,
    field?: string,
    details?: any
  ) {
    return new AdminApiError(message, code, statusCode, field, details);
  }

  static handle(error: any) {
    return handleAdminError(error);
  }

  static log(error: any, context?: string) {
    logError(error, context);
  }

  static wrap<T extends any[], R>(handler: (...args: T) => Promise<R>) {
    return withErrorHandling(handler);
  }
}
