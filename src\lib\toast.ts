"use client";

// Compatibility layer for Sonner toast API
// This provides the same API as Sonner but uses our custom toast implementation

export const toast = {
  success: (message: string, options?: { duration?: number }) => {
    if (typeof window !== "undefined" && (window as any).toastApi) {
      (window as any).toastApi.toast.success(message, options?.duration);
    } else {
      console.log("Toast Success:", message);
    }
  },
  error: (message: string, options?: { duration?: number }) => {
    if (typeof window !== "undefined" && (window as any).toastApi) {
      (window as any).toastApi.toast.error(message, options?.duration);
    } else {
      console.error("Toast Error:", message);
    }
  },
  info: (message: string, options?: { duration?: number }) => {
    if (typeof window !== "undefined" && (window as any).toastApi) {
      (window as any).toastApi.toast.info(message, options?.duration);
    } else {
      console.log("Toast Info:", message);
    }
  },
  warning: (message: string, options?: { duration?: number }) => {
    if (typeof window !== "undefined" && (window as any).toastApi) {
      (window as any).toastApi.toast.warning(message, options?.duration);
    } else {
      console.warn("Toast Warning:", message);
    }
  },
};
