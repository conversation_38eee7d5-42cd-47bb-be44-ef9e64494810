"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Download,
  RefreshCw,
} from "lucide-react";
import { toast } from "@/lib/toast";
import {
  Analytics<PERSON>hart,
  StatusChart,
  TopItemsChart,
} from "@/components/admin/analytics-chart";

interface AnalyticsData {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    totalProducts: number;
    revenueGrowth: number;
    ordersGrowth: number;
    customersGrowth: number;
  };
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    totalSold: number;
    revenue: number;
  }>;
  topCategories: Array<{
    id: string;
    name: string;
    productCount: number;
    revenue: number;
  }>;
  ordersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  customerStats: {
    newCustomers: number;
    returningCustomers: number;
    averageOrderValue: number;
  };
}

export default function AdminAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d"); // 7d, 30d, 90d, 1y

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/admin/analytics?range=${timeRange}`, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          setError("Phiên đăng nhập đã hết hạn");
          toast.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.");
          setTimeout(() => {
            window.location.href = "/admin/auth/signin";
          }, 2000);
          return;
        } else if (response.status === 403) {
          setError("Bạn không có quyền truy cập trang này");
          toast.error("Bạn không có quyền truy cập trang này");
          return;
        } else {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage =
            errorData.error || "Có lỗi xảy ra khi tải dữ liệu thống kê";
          setError(errorMessage);
          toast.error(errorMessage);
          return;
        }
      }

      const data = await response.json();
      setAnalytics(data);
      setError(null);
    } catch (error) {
      console.error("Analytics fetch error:", error);
      const errorMessage = "Không thể kết nối đến server. Vui lòng thử lại.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("vi-VN").format(num);
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return "text-green-600";
    if (growth < 0) return "text-red-600";
    return "text-gray-600";
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4" />;
    if (growth < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Thống kê & Báo cáo</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }, (_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2" />
                <div className="h-8 bg-gray-200 rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Thống kê & Báo cáo</h1>
            <p className="text-muted-foreground">
              Phân tích chi tiết hoạt động kinh doanh
            </p>
          </div>
        </div>

        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <div className="h-5 w-5 text-red-600">⚠️</div>
              <div>
                <h3 className="font-medium text-red-800">Có lỗi xảy ra</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchAnalytics}
                  className="mt-3"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Thử lại
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Không có dữ liệu thống kê</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Thống kê & Báo cáo</h1>
          <p className="text-muted-foreground">
            Phân tích chi tiết hoạt động kinh doanh
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            <option value="7d">7 ngày qua</option>
            <option value="30d">30 ngày qua</option>
            <option value="90d">90 ngày qua</option>
            <option value="1y">1 năm qua</option>
          </select>
          <Button variant="outline" onClick={fetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Download className="h-4 w-4 mr-2" />
            Xuất báo cáo
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        data-testid="summary-cards"
      >
        <Card data-testid="revenue-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tổng doanh thu
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPrice(analytics.overview.totalRevenue)}
            </div>
            <p
              className={`text-xs flex items-center gap-1 ${getGrowthColor(
                analytics.overview.revenueGrowth
              )}`}
            >
              {getGrowthIcon(analytics.overview.revenueGrowth)}
              {Math.abs(analytics.overview.revenueGrowth).toFixed(1)}% so với kỳ
              trước
            </p>
          </CardContent>
        </Card>

        <Card data-testid="orders-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn hàng</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics.overview.totalOrders)}
            </div>
            <p
              className={`text-xs flex items-center gap-1 ${getGrowthColor(
                analytics.overview.ordersGrowth
              )}`}
            >
              {getGrowthIcon(analytics.overview.ordersGrowth)}
              {Math.abs(analytics.overview.ordersGrowth).toFixed(1)}% so với kỳ
              trước
            </p>
          </CardContent>
        </Card>

        <Card data-testid="customers-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khách hàng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics.overview.totalCustomers)}
            </div>
            <p
              className={`text-xs flex items-center gap-1 ${getGrowthColor(
                analytics.overview.customersGrowth
              )}`}
            >
              {getGrowthIcon(analytics.overview.customersGrowth)}
              {Math.abs(analytics.overview.customersGrowth).toFixed(1)}% so với
              kỳ trước
            </p>
          </CardContent>
        </Card>

        <Card data-testid="products-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sản phẩm</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics.overview.totalProducts)}
            </div>
            <p className="text-xs text-muted-foreground">Sản phẩm đang bán</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Revenue Chart */}
        <AnalyticsChart
          data={analytics.monthlyRevenue}
          title="Doanh thu theo tháng"
          type="revenue"
        />

        {/* Monthly Orders Chart */}
        <AnalyticsChart
          data={analytics.monthlyRevenue}
          title="Đơn hàng theo tháng"
          type="orders"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Categories */}
        <TopItemsChart
          data={analytics.topCategories.map((category) => ({
            id: category.id,
            name: category.name,
            value: category.revenue,
            label: `${category.productCount} sản phẩm`,
          }))}
          title="Danh mục bán chạy"
          valueFormatter={(value) => formatPrice(value)}
        />

        {/* Customer Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Thống kê khách hàng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Khách hàng mới</span>
                <span className="font-medium">
                  {analytics.customerStats.newCustomers}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Khách hàng quay lại
                </span>
                <span className="font-medium">
                  {analytics.customerStats.returningCustomers}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Giá trị đơn hàng TB
                </span>
                <span className="font-medium">
                  {formatPrice(analytics.customerStats.averageOrderValue)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <TopItemsChart
          data={analytics.topProducts.map((product) => ({
            id: product.id,
            name: product.name,
            value: product.revenue,
            label: `${product.totalSold} đã bán`,
          }))}
          title="Sản phẩm bán chạy"
          valueFormatter={(value) => formatPrice(value)}
        />

        {/* Order Status Distribution */}
        <StatusChart
          data={analytics.ordersByStatus}
          title="Phân bố trạng thái đơn hàng"
        />
      </div>
    </div>
  );
}
