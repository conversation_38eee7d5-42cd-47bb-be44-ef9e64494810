import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/products/[slug] - <PERSON><PERSON><PERSON> chi tiết sản phẩm theo slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json({ error: "Slug không hợp lệ" }, { status: 400 });
    }

    // Get product by slug with all related data
    const product = await prisma.product.findUnique({
      where: {
        slug,
        status: "ACTIVE", // Only return active products
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10, // Limit to 10 most recent reviews
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: "<PERSON>hông tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    // Calculate average rating
    const avgRating =
      product.reviews.length > 0
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) /
          product.reviews.length
        : 0;

    // Format response
    const formattedProduct = {
      ...product,
      avgRating: Math.round(avgRating * 10) / 10, // Round to 1 decimal place
      reviewCount: product._count.reviews,
    };

    return NextResponse.json(formattedProduct);
  } catch (error) {
    console.error("Get product by slug error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin sản phẩm" },
      { status: 500 }
    );
  }
}
