import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// GET /api/orders/[id] - <PERSON><PERSON><PERSON> chi tiết đơn hàng
export async function GET(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const order = await prisma.order.findUnique({
			where: { id: params.id },
			include: {
				items: {
					include: {
						product: {
							select: {
								id: true,
								name: true,
								images: true,
								slug: true,
							},
						},
					},
				},
			},
		});

		if (!order) {
			return NextResponse.json(
				{ error: 'Không tìm thấy đơn hàng' },
				{ status: 404 }
			);
		}

		// Check if user owns this order or is admin
		if (order.userId !== session.user.id && session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		return NextResponse.json(order);
	} catch (error) {
		console.error('Get order error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy thông tin đơn hàng' },
			{ status: 500 }
		);
	}
}
