import { Page, expect, Locator } from "@playwright/test";
import { PageData } from "../pages/admin-pages.page";

/**
 * Utilities for interacting with page forms and validation
 */

/**
 * Form validation helper
 */
export class PageFormValidator {
  constructor(private page: Page) {}

  /**
   * Validate required field errors
   */
  async expectRequiredFieldErrors(): Promise<void> {
    // Title is required
    await expect(
      this.page
        .locator(".text-red-500, .error-message")
        .filter({ hasText: /Tiêu đề.*bắt buộc/ })
    ).toBeVisible();

    // Content is required
    await expect(
      this.page
        .locator(".text-red-500, .error-message")
        .filter({ hasText: /Nội dung.*bắt buộc/ })
    ).toBeVisible();
  }

  /**
   * Validate slug format
   */
  async expectSlugFormatError(): Promise<void> {
    await expect(
      this.page
        .locator(".text-red-500, .error-message")
        .filter({ hasText: /Slug.*không hợp lệ/ })
    ).toBeVisible();
  }

  /**
   * Validate duplicate slug error
   */
  async expectDuplicateSlugError(): Promise<void> {
    await expect(
      this.page
        .locator(".text-red-500, .error-message")
        .filter({ hasText: /Slug.*đã tồn tại/ })
    ).toBeVisible();
  }

  /**
   * Validate form has no errors
   */
  async expectNoValidationErrors(): Promise<void> {
    await expect(
      this.page.locator(".text-red-500, .error-message")
    ).toHaveCount(0);
  }
}

/**
 * Rich text editor helper
 */
export class RichTextEditorHelper {
  private editor: Locator;

  constructor(
    private page: Page,
    editorSelector: string = ".ProseMirror"
  ) {
    this.editor = page.locator(editorSelector);
  }

  /**
   * Set content in rich text editor
   */
  async setContent(content: string): Promise<void> {
    // TipTap ProseMirror editor
    await this.editor.click();
    await this.editor.fill("");
    await this.editor.type(content);
  }

  /**
   * Get content from rich text editor
   */
  async getContent(): Promise<string> {
    if ((await this.editor.locator("iframe").count()) > 0) {
      const frame = this.editor.locator("iframe").first();
      return (await frame.locator("body").textContent()) || "";
    } else if (
      (await this.editor.locator('[contenteditable="true"]').count()) > 0
    ) {
      return (
        (await this.editor.locator('[contenteditable="true"]').textContent()) ||
        ""
      );
    } else {
      return await this.editor.inputValue();
    }
  }

  /**
   * Insert text at cursor position
   */
  async insertText(text: string): Promise<void> {
    await this.editor.focus();
    await this.page.keyboard.type(text);
  }

  /**
   * Clear editor content
   */
  async clear(): Promise<void> {
    if ((await this.editor.locator("iframe").count()) > 0) {
      const frame = this.editor.locator("iframe").first();
      await frame.locator("body").clear();
    } else if (
      (await this.editor.locator('[contenteditable="true"]').count()) > 0
    ) {
      await this.editor.locator('[contenteditable="true"]').clear();
    } else {
      await this.editor.clear();
    }
  }
}

/**
 * Status and feature toggle helper
 */
export class PageStatusHelper {
  constructor(private page: Page) {}

  /**
   * Set page status
   */
  async setStatus(status: "DRAFT" | "PUBLISHED" | "ARCHIVED"): Promise<void> {
    const statusSelect = this.page.locator(
      'select[name="status"], [data-testid="status-select"]'
    );

    if ((await statusSelect.count()) > 0) {
      await statusSelect.selectOption(status);
    } else {
      // Handle custom dropdown
      const statusDropdown = this.page.locator(
        '[data-testid="status-dropdown"]'
      );
      await statusDropdown.click();
      await this.page.locator(`[data-value="${status}"]`).click();
    }
  }

  /**
   * Get current status
   */
  async getCurrentStatus(): Promise<string> {
    const statusSelect = this.page.locator('select[name="status"]');

    if ((await statusSelect.count()) > 0) {
      return await statusSelect.inputValue();
    } else {
      // Handle custom dropdown
      const statusText = this.page.locator('[data-testid="status-display"]');
      return (await statusText.textContent()) || "";
    }
  }

  /**
   * Toggle featured status
   */
  async toggleFeatured(featured: boolean): Promise<void> {
    const featuredToggle = this.page.locator('button[id="featured"]');
    const currentState = await featuredToggle.getAttribute("data-state");
    const isCurrentlyChecked = currentState === "checked";

    if (featured && !isCurrentlyChecked) {
      await featuredToggle.click();
    } else if (!featured && isCurrentlyChecked) {
      await featuredToggle.click();
    }
  }

  /**
   * Check if page is featured
   */
  async isFeatured(): Promise<boolean> {
    const featuredToggle = this.page.locator('button[id="featured"]');
    const currentState = await featuredToggle.getAttribute("data-state");
    return currentState === "checked";
  }
}

/**
 * Bulk operations helper
 */
export class BulkOperationsHelper {
  constructor(private page: Page) {}

  /**
   * Select pages by titles
   */
  async selectPagesByTitles(titles: string[]): Promise<void> {
    for (const title of titles) {
      const row = this.page.locator("tbody tr").filter({ hasText: title });
      await row.locator('input[type="checkbox"]').check();
    }
  }

  /**
   * Select all visible pages
   */
  async selectAllPages(): Promise<void> {
    const selectAllCheckbox = this.page.locator('thead input[type="checkbox"]');
    await selectAllCheckbox.check();
  }

  /**
   * Deselect all pages
   */
  async deselectAllPages(): Promise<void> {
    const selectAllCheckbox = this.page.locator('thead input[type="checkbox"]');
    await selectAllCheckbox.uncheck();
  }

  /**
   * Get selected page count
   */
  async getSelectedCount(): Promise<number> {
    return await this.page
      .locator('tbody tr input[type="checkbox"]:checked')
      .count();
  }

  /**
   * Bulk delete selected pages
   */
  async bulkDeleteSelected(): Promise<void> {
    // Click bulk actions dropdown
    const bulkActionsButton = this.page
      .locator("button")
      .filter({ hasText: /Thao tác \(\d+\)/ });
    await bulkActionsButton.click();

    // Click delete option
    const deleteOption = this.page
      .locator('[role="menuitem"]')
      .filter({ hasText: "Xóa" });
    await deleteOption.click();

    // Confirm deletion
    const confirmButton = this.page
      .locator("button")
      .filter({ hasText: /Xác nhận|Xóa/ });
    await confirmButton.click();

    // Wait for success message
    await expect(
      this.page.locator(".bg-green-50.border-green-200.text-green-800")
    ).toBeVisible();
  }

  /**
   * Bulk change status of selected pages
   */
  async bulkChangeStatus(
    status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
  ): Promise<void> {
    // Click bulk actions dropdown
    const bulkActionsButton = this.page
      .locator("button")
      .filter({ hasText: /Thao tác \(\d+\)/ });
    await bulkActionsButton.click();

    // Click status change option
    const statusOption = this.page
      .locator('[role="menuitem"]')
      .filter({ hasText: "Thay đổi trạng thái" });
    await statusOption.click();

    // Select new status
    const statusSelect = this.page.locator(`[data-value="${status}"]`);
    await statusSelect.click();

    // Wait for success message
    await expect(
      this.page.locator(".bg-green-50.border-green-200.text-green-800")
    ).toBeVisible();
  }
}

/**
 * Search and filter helper
 */
export class SearchFilterHelper {
  constructor(private page: Page) {}

  /**
   * Search pages by query
   */
  async searchPages(query: string): Promise<void> {
    const searchInput = this.page.locator(
      'input[placeholder="Tìm kiếm trang..."]'
    );
    await searchInput.fill(query);
    await this.page.keyboard.press("Enter");
    await this.waitForResults();
  }

  /**
   * Filter by status (not implemented in current UI)
   */
  async filterByStatus(
    status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "ALL"
  ): Promise<void> {
    // This feature is not implemented in the current UI
    // Skip for now
    console.log(`Filter by status ${status} - not implemented`);
  }

  /**
   * Filter by featured status (not implemented in current UI)
   */
  async filterByFeatured(featured: boolean): Promise<void> {
    // This feature is not implemented in the current UI
    // Skip for now
    console.log(`Filter by featured ${featured} - not implemented`);
  }

  /**
   * Clear all filters
   */
  async clearFilters(): Promise<void> {
    const clearButton = this.page
      .locator("button")
      .filter({ hasText: "Xóa bộ lọc" });
    await clearButton.click();
    await this.waitForResults();
  }

  /**
   * Wait for search/filter results
   */
  private async waitForResults(): Promise<void> {
    await this.page.waitForTimeout(500); // Debounce
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Get current search query
   */
  async getCurrentSearchQuery(): Promise<string> {
    const searchInput = this.page.locator('input[placeholder*="Tìm kiếm"]');
    return await searchInput.inputValue();
  }

  /**
   * Get visible page count after search/filter
   */
  async getVisiblePageCount(): Promise<number> {
    return await this.page.locator("tbody tr").count();
  }
}

/**
 * Pagination helper
 */
export class PaginationHelper {
  constructor(private page: Page) {}

  /**
   * Go to specific page
   */
  async goToPage(pageNumber: number): Promise<void> {
    const pageButton = this.page.locator(`button[data-page="${pageNumber}"]`);
    await pageButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Go to next page
   */
  async goToNextPage(): Promise<void> {
    const nextButton = this.page
      .locator("button")
      .filter({ hasText: "Tiếp theo" });
    await nextButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage(): Promise<void> {
    const prevButton = this.page.locator("button").filter({ hasText: "Trước" });
    await prevButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Get current page number
   */
  async getCurrentPage(): Promise<number> {
    const currentPageButton = this.page.locator('button[aria-current="page"]');
    const pageText = await currentPageButton.textContent();
    return parseInt(pageText || "1");
  }

  /**
   * Get total page count
   */
  async getTotalPages(): Promise<number> {
    const pageButtons = this.page.locator("button[data-page]");
    const count = await pageButtons.count();
    return count;
  }

  /**
   * Change page size
   */
  async changePageSize(size: number): Promise<void> {
    const pageSizeSelect = this.page.locator('select[data-testid="page-size"]');
    await pageSizeSelect.selectOption(size.toString());
    await this.waitForPageLoad();
  }

  /**
   * Wait for page to load after pagination
   */
  private async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState("networkidle");
  }
}
